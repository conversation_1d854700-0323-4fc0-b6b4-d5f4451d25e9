/**
 * 生产数据清理云函数
 * 安全清理测试数据，保留系统配置
 */

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('🚀 开始生产数据清理...');
  
  const { action = 'clear', confirm = false } = event;
  
  // 安全检查
  if (!confirm) {
    return {
      success: false,
      message: '请确认清理操作（设置 confirm: true）',
      code: 'CONFIRMATION_REQUIRED'
    };
  }
  
  if (action !== 'clear') {
    return {
      success: false,
      message: '无效的操作类型',
      code: 'INVALID_ACTION'
    };
  }
  
  // 清理统计
  let totalCleared = 0;
  let errors = [];
  let clearingLog = [];
  
  try {
    // 第一阶段：清理隐私数据（最高优先级）
    console.log('🚨 第一阶段：清理隐私数据...');
    clearingLog.push('🚨 第一阶段：清理隐私数据...');
    
    const privacyCollections = ['users', 'students', 'user_profiles', 'user_consent'];
    
    for (const collection of privacyCollections) {
      try {
        console.log(`🗑️ 正在清理 ${collection}...`);
        clearingLog.push(`🗑️ 正在清理 ${collection}...`);
        
        let hasMore = true;
        let cleared = 0;
        
        while (hasMore) {
          // 获取一批数据
          const result = await db.collection(collection)
            .limit(20)
            .get();
          
          if (result.data.length === 0) {
            hasMore = false;
          } else {
            // 批量删除
            for (const doc of result.data) {
              try {
                await db.collection(collection).doc(doc._id).remove();
                cleared++;
                totalCleared++;
              } catch (deleteError) {
                console.error(`删除文档失败: ${collection}/${doc._id}`, deleteError);
              }
            }
          }
        }
        
        console.log(`✅ ${collection}: 已清理 ${cleared} 条记录`);
        clearingLog.push(`✅ ${collection}: 已清理 ${cleared} 条记录`);
        
      } catch (error) {
        console.error(`❌ 清理 ${collection} 失败:`, error);
        clearingLog.push(`❌ 清理 ${collection} 失败: ${error.message}`);
        errors.push({ collection, error: error.message, phase: '隐私数据' });
      }
    }
    
    // 第二阶段：清理测试内容
    console.log('🟡 第二阶段：清理测试内容...');
    clearingLog.push('🟡 第二阶段：清理测试内容...');
    
    const contentCollections = ['comments', 'records', 'classes'];
    
    for (const collection of contentCollections) {
      try {
        console.log(`🗑️ 正在清理 ${collection}...`);
        clearingLog.push(`🗑️ 正在清理 ${collection}...`);
        
        let hasMore = true;
        let cleared = 0;
        
        while (hasMore) {
          const result = await db.collection(collection)
            .limit(20)
            .get();
          
          if (result.data.length === 0) {
            hasMore = false;
          } else {
            for (const doc of result.data) {
              try {
                await db.collection(collection).doc(doc._id).remove();
                cleared++;
                totalCleared++;
              } catch (deleteError) {
                console.error(`删除文档失败: ${collection}/${doc._id}`, deleteError);
              }
            }
          }
        }
        
        console.log(`✅ ${collection}: 已清理 ${cleared} 条记录`);
        clearingLog.push(`✅ ${collection}: 已清理 ${cleared} 条记录`);
        
      } catch (error) {
        console.error(`❌ 清理 ${collection} 失败:`, error);
        clearingLog.push(`❌ 清理 ${collection} 失败: ${error.message}`);
        errors.push({ collection, error: error.message, phase: '测试内容' });
      }
    }
    
    // 第三阶段：清理统计数据
    console.log('🟢 第三阶段：清理统计数据...');
    clearingLog.push('🟢 第三阶段：清理统计数据...');
    
    const statsCollections = ['usage_stats', 'cost_stats', 'growth_stats', 'achievements', 'monitoring'];
    
    for (const collection of statsCollections) {
      try {
        console.log(`🗑️ 正在清理 ${collection}...`);
        clearingLog.push(`🗑️ 正在清理 ${collection}...`);
        
        let hasMore = true;
        let cleared = 0;
        
        while (hasMore) {
          const result = await db.collection(collection)
            .limit(20)
            .get();
          
          if (result.data.length === 0) {
            hasMore = false;
          } else {
            for (const doc of result.data) {
              try {
                await db.collection(collection).doc(doc._id).remove();
                cleared++;
                totalCleared++;
              } catch (deleteError) {
                console.error(`删除文档失败: ${collection}/${doc._id}`, deleteError);
              }
            }
          }
        }
        
        console.log(`✅ ${collection}: 已清理 ${cleared} 条记录`);
        clearingLog.push(`✅ ${collection}: 已清理 ${cleared} 条记录`);
        
      } catch (error) {
        console.error(`❌ 清理 ${collection} 失败:`, error);
        clearingLog.push(`❌ 清理 ${collection} 失败: ${error.message}`);
        errors.push({ collection, error: error.message, phase: '统计数据' });
      }
    }
    
    // 验证保留的系统配置
    console.log('✅ 验证保留的系统配置...');
    clearingLog.push('✅ 验证保留的系统配置...');
    
    const preservedCollections = ['prompt_templates', 'ai_config', 'system_config'];
    const preservedData = {};
    
    for (const collection of preservedCollections) {
      try {
        const result = await db.collection(collection).count();
        preservedData[collection] = result.total;
        console.log(`📋 ${collection}: 保留 ${result.total} 条配置记录`);
        clearingLog.push(`📋 ${collection}: 保留 ${result.total} 条配置记录`);
      } catch (error) {
        console.log(`⚠️ ${collection}: 集合可能不存在或为空`);
        clearingLog.push(`⚠️ ${collection}: 集合可能不存在或为空`);
        preservedData[collection] = 0;
      }
    }
    
    // 生成清理报告
    const report = {
      success: true,
      timestamp: new Date().toISOString(),
      summary: {
        totalCleared,
        errorsCount: errors.length,
        preservedCollections: preservedData
      },
      details: {
        clearingLog,
        errors
      },
      message: `数据清理完成！总计清理 ${totalCleared} 条记录，${errors.length} 个错误`
    };
    
    console.log('🎉 数据清理完成！');
    console.log(`📊 总计清理记录: ${totalCleared} 条`);
    console.log(`❌ 清理错误: ${errors.length} 个`);
    
    return report;
    
  } catch (error) {
    console.error('清理过程发生严重错误:', error);
    
    return {
      success: false,
      message: `清理过程发生严重错误: ${error.message}`,
      error: error.message,
      totalCleared,
      errors,
      clearingLog
    };
  }
};
