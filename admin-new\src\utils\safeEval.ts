/**
 * 安全的代码执行工具，避免使用eval
 * 用于替换项目中所有可能的eval使用场景
 */

// 安全的表达式计算器（白名单方法）
interface SafeContext {
  [key: string]: any;
}

// 预定义的安全函数白名单
const SAFE_FUNCTIONS = {
  // 数学运算
  Math: Math,
  parseInt: parseInt,
  parseFloat: parseFloat,
  isNaN: isNaN,
  isFinite: isFinite,
  
  // 字符串操作
  String: String,
  encodeURIComponent: encodeURIComponent,
  decodeURIComponent: decodeURIComponent,
  
  // 数组操作
  Array: Array,
  
  // 日期操作
  Date: Date,
  
  // JSON操作
  JSON: JSON
};

/**
 * 安全的模板字符串解析器
 * 替代模板字符串中的动态表达式执行
 */
export function safeTemplateParser(template: string, context: SafeContext = {}): string {
  // 使用正则表达式匹配 ${} 模板语法
  return template.replace(/\$\{([^}]+)\}/g, (match, expression) => {
    try {
      // 移除可能的危险字符
      const cleanExpression = expression.trim();
      
      // 只允许简单的属性访问和基本运算
      if (!/^[a-zA-Z_$][a-zA-Z0-9_$.[\]]*$/.test(cleanExpression.replace(/\s/g, ''))) {

        return match;
      }
      
      // 安全地获取值
      return getNestedValue(context, cleanExpression) || '';
    } catch (error) {
      console.error(`模板解析错误: ${expression}`, error);
      return match;
    }
  });
}

/**
 * 安全的嵌套属性访问
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : null;
  }, obj);
}

/**
 * 安全的函数构造器（替代 new Function）
 * 仅允许预定义的安全操作
 */
export function createSafeFunction(
  functionBody: string, 
  allowedContext: SafeContext = {}
): (() => any) | null {
  try {
    // 检查函数体是否包含危险操作
    const dangerousPatterns = [
      /eval\s*\(/,
      /Function\s*\(/,
      /setTimeout\s*\(/,
      /setInterval\s*\(/,
      /document\./,
      /window\./,
      /global\./,
      /process\./,
      /require\s*\(/,
      /import\s*\(/
    ];
    
    for (const pattern of dangerousPatterns) {
      if (pattern.test(functionBody)) {

        return null;
      }
    }
    
    // 创建安全的执行环境
    const safeContext = { ...SAFE_FUNCTIONS, ...allowedContext };
    
    // 使用闭包模拟安全的函数执行
    return function() {
      try {
        // 这里可以添加更复杂的安全解析逻辑
        // 目前返回一个空函数作为占位

        return null;
      } catch (error) {
        console.error('安全函数执行错误:', error);
        return null;
      }
    };
  } catch (error) {
    console.error('创建安全函数失败:', error);
    return null;
  }
}

/**
 * 安全的配置对象解析器
 * 替代动态配置加载中的eval使用
 */
export function safeConfigParser(configString: string): any {
  try {
    // 首先尝试JSON解析
    return JSON.parse(configString);
  } catch (jsonError) {

    // 如果不是有效JSON，进行安全的键值对解析
    const config: any = {};
    const lines = configString.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('//') && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split(':');
        if (key && valueParts.length > 0) {
          const value = valueParts.join(':').trim();
          
          // 安全地解析值
          if (value === 'true') config[key.trim()] = true;
          else if (value === 'false') config[key.trim()] = false;
          else if (!isNaN(Number(value))) config[key.trim()] = Number(value);
          else config[key.trim()] = value.replace(/^["']|["']$/g, ''); // 移除引号
        }
      }
    }
    
    return config;
  }
}

/**
 * 安全的动态样式生成器
 * 替代可能使用eval的CSS-in-JS场景
 */
export function safeDynamicStyles(styleTemplate: string, variables: Record<string, string | number>): string {
  let result = styleTemplate;
  
  // 只允许预定义的CSS属性变量替换
  Object.entries(variables).forEach(([key, value]) => {
    // 验证CSS属性名的安全性
    if (/^[a-zA-Z-]+$/.test(key)) {
      // 验证CSS值的安全性
      const safeValue = String(value).replace(/[<>'"]/g, '');
      result = result.replace(new RegExp(`\\$\\{${key}\\}`, 'g'), safeValue);
    }
  });
  
  return result;
}

/**
 * 开发环境检测
 */
export const isDevelopment = () => {
  return process.env.NODE_ENV === 'development' || __DEV__;
};

/**
 * 安全的调试日志（开发环境）
 */
export function safeDebugLog(message: string, data?: any): void {
  if (isDevelopment()) {

  }
}