/**
 * 记录详情页面
 */
const app = getApp();
const { cloudService } = require('../../../services/cloudService');
const { extractSurname } = require('../../../utils/globalUtils');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    recordId: null,
    recordInfo: {},
    relatedRecords: [],
    operationHistory: [],

    // 操作菜单
    showActionSheet: false,
    actionSheetActions: [
      { name: '编辑记录', value: 'edit' },
      { name: '生成评语', value: 'comment' },
      { name: '删除记录', value: 'delete', color: '#FF5247' }
    ],

    // 删除确认
    showDeleteDialog: false,

    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ recordId: id });
      this.loadRecordDetail(id);
    }
  },

  /**
   * 加载记录详情
   */
  async loadRecordDetail(recordId) {
    try {
      this.setData({ loading: true });

      // 从云数据库获取记录详情
      const result = await cloudService.getRecordList({ pageSize: 1000 });

      if (!result.success) {
        throw new Error(result.error || '获取记录失败');
      }

      // 查找指定的记录
      const record = result.data.find(r => r._id === recordId);

      if (!record) {
        throw new Error('记录不存在');
      }

      // 处理记录数据
      const recordInfo = {
        id: record._id,
        studentName: record.studentName,
        studentId: record.studentId,
        className: record.className,
        studentAvatar: record.studentAvatar || '',
        surname: extractSurname(record.studentName || ''),
        behaviorType: record.behaviorType || record.type,
        typeName: this.getBehaviorTypeText(record.behaviorType || record.type),
        typeIcon: this.getBehaviorTypeIcon(record.behaviorType || record.type),
        action: record.action || record.category,
        description: record.description || record.content || '',
        score: record.score || 0,
        createTime: this.formatTime(record.createTime),
        teacherName: record.teacherName || '老师',
        images: this.filterValidImages(record.images || [])
      };

      // 获取该学生的其他记录（相关记录）
      const relatedRecords = result.data
        .filter(r => r.studentId === record.studentId && r._id !== recordId)
        .slice(0, 5) // 最多显示5条相关记录
        .map(r => ({
          id: r._id,
          action: r.action || r.category,
          behaviorType: r.behaviorType || r.type,
          typeIcon: this.getBehaviorTypeIcon(r.behaviorType || r.type),
          score: r.score || 0,
          createTime: this.formatTime(r.createTime)
        }));

      const operationHistory = [
        {
          id: 1,
          action: '创建记录',
          icon: 'plus',
          time: recordInfo.createTime,
          user: recordInfo.teacherName
        }
      ];

      this.setData({
        recordInfo,
        relatedRecords,
        operationHistory,
        loading: false
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: `${recordInfo.studentName || '学生'} - ${recordInfo.action || '记录详情'}`
      });

    } catch (error) {
      console.error('加载记录详情失败:', error);
      this.setData({ loading: false });
      
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });

      // 如果记录不存在，延迟返回上级页面
      if (error.message === '记录不存在') {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    }
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const { index } = e.currentTarget.dataset;
    const { images } = this.data.recordInfo;

    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  /**
   * 编辑记录
   */
  editRecord() {
    wx.navigateTo({
      url: `/pages/record/create/create?mode=edit&id=${this.data.recordId}`
    });
  },

  /**
   * 生成评语
   */
  generateComment() {
    const { recordInfo } = this.data;
    wx.navigateTo({
      url: `/pages/comment/generate/generate?studentId=${recordInfo.studentId}&recordId=${recordInfo.id}`
    });
  },

  /**
   * 跳转到相关记录
   */
  goToRecord(e) {
    const { record } = e.currentTarget.dataset;
    wx.redirectTo({
      url: `/pages/record/detail/detail?id=${record.id}`
    });
  },

  /**
   * 显示操作菜单
   */
  showActionSheet() {
    this.setData({ showActionSheet: true });
  },

  /**
   * 隐藏操作菜单
   */
  hideActionSheet() {
    this.setData({ showActionSheet: false });
  },

  /**
   * 处理操作选择
   */
  onActionSelect(e) {
    const { value } = e.detail;
    const { recordInfo } = this.data;

    this.hideActionSheet();

    switch (value) {
      case 'edit':
        this.editRecord();
        break;
      case 'comment':
        this.generateComment();
        break;
      case 'delete':
        this.showDeleteDialog();
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  /**
   * 显示删除确认
   */
  showDeleteDialog() {
    this.setData({ showDeleteDialog: true });
  },

  /**
   * 取消删除
   */
  cancelDelete() {
    this.setData({ showDeleteDialog: false });
  },

  /**
   * 确认删除
   */
  async confirmDelete() {
    try {
      this.setData({ showDeleteDialog: false });

      wx.showLoading({
        title: '删除中...',
        mask: true
      });

      // 调用云服务删除记录
      const result = await cloudService.deleteRecord(this.data.recordId);

      wx.hideLoading();

      if (result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(result.error || '删除失败');
      }

    } catch (error) {
      console.error('删除记录失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const { url, index } = e.currentTarget.dataset;
    const { recordInfo } = this.data;

    if (recordInfo && recordInfo.images && recordInfo.images.length > 0) {
      wx.previewImage({
        urls: recordInfo.images,
        current: url || recordInfo.images[index || 0]
      });
    }
  },

  /**
   * 过滤有效图片
   */
  filterValidImages(images) {

    if (!Array.isArray(images)) {

      return [];
    }

    const validImages = images.filter(img => {
      // 过滤掉无效的图片路径
      if (!img || typeof img !== 'string') {

        return false;
      }

      // 过滤掉临时路径和无效路径
      if (img.includes('__tmp__') || img.includes('127.0.0.1')) {

        return false;
      }

      // 保留有效的图片路径（放宽条件，包含微信临时文件）
      const isValid = img.startsWith('cloud://') ||
                     img.startsWith('https://') ||
                     img.startsWith('http://') ||
                     img.startsWith('/') ||
                     img.startsWith('wxfile://') ||
                     img.includes('tmp_') ||
                     img.includes('tempFilePath') ||
                     img.includes('store_') ||
                     img.includes('usr/');

      return isValid;
    });

    return validImages;
  },

  /**
   * 图片加载错误处理
   */
  onImageError(e) {
    const { src } = e.currentTarget.dataset;

    // 隐藏加载失败的图片
    const { recordInfo } = this.data;
    if (recordInfo && recordInfo.images) {
      const validImages = recordInfo.images.filter(img => img !== src);
      this.setData({
        'recordInfo.images': validImages
      });
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 获取行为类型文本
   */
  getBehaviorTypeText(type) {
    const typeMap = {
      'positive': '积极行为',
      'negative': '消极行为',
      'neutral': '中性行为',
      'academic': '学习表现'
    };
    return typeMap[type] || '未知类型';
  },

  /**
   * 获取行为类型图标
   */
  getBehaviorTypeIcon(type) {
    const iconMap = {
      'positive': 'like-o',
      'negative': 'warning-o',
      'neutral': 'info-o',
      'academic': 'edit'
    };
    return iconMap[type] || 'info-o';
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    try {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hour = String(date.getHours()).padStart(2, '0');
      const minute = String(date.getMinutes()).padStart(2, '0');
      const second = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    } catch (error) {
      console.error('时间格式化失败:', error);
      return String(timestamp);
    }
  }
});