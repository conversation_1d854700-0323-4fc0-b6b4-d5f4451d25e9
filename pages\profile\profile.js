/**
 * 个人中心页面
 * 基于微信云开发
 */
const app = getApp();

// 获取云服务实例的辅助函数
function getCloudService() {
  const cloudService = app.globalData.cloudService;
  if (!cloudService) {
    throw new Error('云服务未初始化，请稍后重试');
  }
  return cloudService;
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},

    // 统计数据（从云数据库获取）
    stats: {
      totalClasses: 0,
      totalStudents: 0,
      totalRecords: 0,
      totalComments: 0
    },

    // 快速编辑
    showQuickEdit: false,
    editForm: {},
    saving: false,

    // 同步时间
    lastSyncTime: '刚刚'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getUserInfo();
    this.loadUserStats();
  },

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    try {
      // 1. 先从云端获取最新数据
      const cloudService = getCloudService();
      const cloudResult = await cloudService.getUserInfo();

      if (cloudResult.success) {
        const cloudUserInfo = cloudResult.data;
        const savedAvatar = wx.getStorageSync('userAvatar');

        const finalUserInfo = {
          nickName: cloudUserInfo.nickName || '老师',
          school: cloudUserInfo.school || '智慧小学',
          role: cloudUserInfo.role || '教师',
          phone: cloudUserInfo.phone || '',
          email: cloudUserInfo.email || '',
          avatar: savedAvatar || cloudUserInfo.avatar || ''
        };

        // 更新本地存储
        wx.setStorageSync('userInfo', finalUserInfo);

        this.setData({
          userInfo: finalUserInfo
        });
        return;
      }
    } catch (error) {
    }

    // 2. 云端获取失败，使用本地存储
    const savedUserInfo = wx.getStorageSync('userInfo') || {};
    const savedAvatar = wx.getStorageSync('userAvatar');

    const finalUserInfo = {
      nickName: savedUserInfo.nickName || '老师',
      school: savedUserInfo.school || '智慧小学',
      role: savedUserInfo.role || '教师',
      phone: savedUserInfo.phone || '',
      email: savedUserInfo.email || '',
      avatar: savedAvatar || savedUserInfo.avatar || ''
    };

    this.setData({
      userInfo: finalUserInfo
    });
  },

  /**
   * 加载用户统计数据
   */
  async loadUserStats() {
    try {
      const cloudService = getCloudService();
      // 并行获取所有统计数据
      const [classResult, studentResult, recordResult] = await Promise.all([
        cloudService.getClassList(),
        cloudService.getStudentList(),
        cloudService.getRecordList({ pageSize: 1000 })
      ]);

      const totalClasses = classResult.success ? classResult.data.length : 0;
      const totalStudents = studentResult.success ? studentResult.data.length : 0;
      const allRecords = recordResult.success ? recordResult.data : [];
      const totalRecords = allRecords.length;

      // 计算有评语的记录数
      const totalComments = allRecords.filter(record =>
        record.hasComment || record.comment
      ).length;

      this.setData({
        stats: {
          totalClasses,
          totalStudents,
          totalRecords,
          totalComments
        }
      });

    } catch (error) {
      console.error('加载统计数据失败:', error);
      // 保持初始值0，不显示错误的模拟数据
    }
  },

  /**
   * 跳转到班级列表
   */
  goToClassList() {
    wx.navigateTo({
      url: '/pages/class/list/list'
    });
  },

  /**
   * 跳转到学生列表
   */
  goToStudentList() {
    wx.navigateTo({
      url: '/pages/student/list/list'
    });
  },

  /**
   * 跳转到记录列表
   */
  goToRecordList() {
    wx.navigateTo({
      url: '/pages/record/list/list'
    });
  },

  /**
   * 跳转到评语列表
   */
  goToCommentList() {
    wx.navigateTo({
      url: '/pages/comment/list/list'
    });
  },

  /**
   * 跳转到数据分析
   */
  goToAnalytics() {
    wx.navigateTo({
      url: '/pages/analytics/overview/overview'
    });
  },

  /**
   * 跳转到导入导出
   */
  goToImportExport() {
    wx.showActionSheet({
      itemList: ['导入学生数据', '导出学生列表', '导出记录数据', '导出评语数据'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            wx.navigateTo({
              url: '/pages/student/create/create?mode=batch'
            });
            break;
          case 1:
          case 2:
          case 3:
            wx.navigateTo({
              url: '/pages/student/list/list'
            });
            // 延迟触发导出
            setTimeout(() => {
              const pages = getCurrentPages();
              const currentPage = pages[pages.length - 1];
              if (currentPage && currentPage.showExportOptions) {
                currentPage.showExportOptions();
              }
            }, 500);
            break;
        }
      }
    });
  },

  /**
   * 跳转到帮助中心
   */
  goToHelp() {
    wx.showModal({
      title: '使用帮助',
      content: '1. 班级管理：创建和管理班级信息\n2. 学生管理：添加学生，支持批量导入\n3. 记录管理：记录学生日常表现\n4. AI评语：智能生成个性化评语\n5. 数据分析：查看统计报告和趋势',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 跳转到关于页面
   */
  goToAbout() {
    wx.showModal({
      title: '关于评语灵感君',
      content: '评语灵感君 v3.0.0\n智能教育协同与洞察平台\n\n基于AI技术的教学辅助工具，帮助教师高效管理班级，智能生成评语，提升教学质量。',
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 更换头像
   */
  changeAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];

        // 保存头像到本地存储
        wx.setStorageSync('userAvatar', tempFilePath);

        // 更新页面数据
        this.setData({
          'userInfo.avatar': tempFilePath
        });

        wx.showToast({
          title: '头像更新成功',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '取消选择',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 快速编辑个人信息
   */
  quickEditProfile() {
    this.setData({
      editForm: {
        nickName: this.data.userInfo.nickName || '',
        school: this.data.userInfo.school || '',
        role: this.data.userInfo.role || '教师'
      },
      showQuickEdit: true
    });
  },

  /**
   * 隐藏快速编辑弹窗
   */
  hideQuickEdit() {
    this.setData({ showQuickEdit: false });
  },

  /**
   * 姓名变化
   */
  onNickNameChange(e) {
    this.setData({
      'editForm.nickName': e.detail || ''
    });
  },

  /**
   * 学校变化
   */
  onSchoolChange(e) {
    this.setData({
      'editForm.school': e.detail || ''
    });
  },

  /**
   * 职位变化
   */
  onRoleChange(e) {
    this.setData({
      'editForm.role': e.detail || ''
    });
  },

  /**
   * 保存快速编辑
   */
  saveQuickEdit() {
    const { editForm } = this.data;

    // 安全获取字段值
    const nickName = (editForm.nickName || '').trim();
    const school = (editForm.school || '').trim();
    const role = (editForm.role || '').trim();

    // 验证必填项
    if (!nickName) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }

    this.setData({ saving: true });

    // 异步保存过程
    setTimeout(async () => {
      try {
        const updatedUserInfo = {
          ...this.data.userInfo,
          nickName: nickName,
          school: school,
          role: role
        };

        // 保存到云端和本地
        const cloudService = getCloudService();
      const saveResult = await cloudService.saveUserInfo(updatedUserInfo);

        if (saveResult.success) {
          // 云端保存成功，更新本地存储
          wx.setStorageSync('userInfo', updatedUserInfo);

          // 更新app中的用户信息
          const app = getApp();
          if (app.globalData) {
            app.globalData.userInfo = updatedUserInfo;
          }
        } else {
          // 云端保存失败，仍然保存到本地
          wx.setStorageSync('userInfo', updatedUserInfo);
          console.error('云端保存失败，已保存到本地:', saveResult.error);
        }

        this.setData({
          userInfo: updatedUserInfo,
          saving: false,
          showQuickEdit: false
        });

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

      } catch (error) {
        console.error('保存用户信息失败:', error);

        // 发生错误时，仍然保存到本地
        const updatedUserInfo = {
          ...this.data.userInfo,
          nickName: nickName,
          school: school,
          role: role
        };

        wx.setStorageSync('userInfo', updatedUserInfo);

        this.setData({
          userInfo: updatedUserInfo,
          saving: false,
          showQuickEdit: false
        });

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      }
    }, 1000);
  },

  /**
   * 数据同步
   */
  async syncData() {
    wx.showLoading({
      title: '同步中...',
      mask: true
    });

    try {
      // 重新从云端获取用户信息和统计数据
      await Promise.all([
        this.getUserInfo(),
        this.loadUserStats()
      ]);

      wx.hideLoading();

      const now = new Date();
      const timeStr = `${now.getMonth() + 1}月${now.getDate()}日 ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;

      this.setData({
        lastSyncTime: timeStr
      });

      wx.showToast({
        title: '同步完成',
        icon: 'success'
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '同步失败',
        icon: 'none'
      });
    }
  },

  /**
   * 数据备份
   */
  async backupData() {
    wx.showLoading({
      title: '备份中...',
      mask: true
    });

    try {
      const cloudService = getCloudService();
      // 获取所有数据
      const [userResult, classResult, studentResult, recordResult] = await Promise.all([
        cloudService.getUserInfo(),
        cloudService.getClassList(),
        cloudService.getStudentList(),
        cloudService.getRecordList({ pageSize: 1000 })
      ]);

      const backupData = {
        version: '3.0.0',
        timestamp: new Date().toISOString(),
        data: {
          user: userResult.success ? userResult.data : null,
          classes: classResult.success ? classResult.data : [],
          students: studentResult.success ? studentResult.data : [],
          records: recordResult.success ? recordResult.data : []
        }
      };

      const backupContent = JSON.stringify(backupData, null, 2);
      const backupSize = new Blob([backupContent]).size;

      wx.hideLoading();

      // 将备份内容复制到剪贴板
      wx.setClipboardData({
        data: backupContent,
        success: () => {
          wx.showModal({
            title: '备份完成',
            content: `数据已复制到剪贴板\n大小：${(backupSize / 1024).toFixed(1)}KB\n包含：${backupData.data.classes.length}个班级，${backupData.data.students.length}名学生，${backupData.data.records.length}条记录\n\n请妥善保存备份数据，换设备时可用于恢复。`,
            showCancel: false,
            confirmText: '知道了'
          });
        },
        fail: () => {
          wx.showToast({
            title: '复制到剪贴板失败',
            icon: 'none'
          });
        }
      });

    } catch (error) {
      console.error('数据备份失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '备份失败',
        icon: 'none'
      });
    }
  },

  /**
   * 数据恢复
   */
  async restoreData() {
    wx.showModal({
      title: '数据恢复',
      content: '请粘贴备份数据进行恢复',
      editable: true,
      placeholderText: '请粘贴完整的备份JSON数据...',
      success: async (res) => {
        if (res.confirm && res.content) {
          wx.showLoading({
            title: '恢复中...',
            mask: true
          });

          try {
            // 解析备份数据
            const backupData = JSON.parse(res.content);

            if (!backupData.version || !backupData.data) {
              throw new Error('备份数据格式错误');
            }

            let restoredCount = 0;

            // 恢复班级数据
            if (backupData.data.classes && backupData.data.classes.length > 0) {
              for (const classData of backupData.data.classes) {
                const cloudService = getCloudService();
      const result = await cloudService.createClass(classData);
                if (result.success) restoredCount++;
              }
            }

            // 恢复学生数据
            if (backupData.data.students && backupData.data.students.length > 0) {
              for (const studentData of backupData.data.students) {
                const cloudService = getCloudService();
      const result = await cloudService.addStudent(studentData);
                if (result.success) restoredCount++;
              }
            }

            // 恢复记录数据
            if (backupData.data.records && backupData.data.records.length > 0) {
              for (const recordData of backupData.data.records) {
                const cloudService = getCloudService();
      const result = await cloudService.createRecord(recordData);
                if (result.success) restoredCount++;
              }
            }

            wx.hideLoading();

            wx.showModal({
              title: '恢复完成',
              content: `成功恢复 ${restoredCount} 条数据\n包含班级、学生和记录信息`,
              showCancel: false,
              confirmText: '重启应用',
              success: () => {
                wx.reLaunch({
                  url: '/pages/index/index'
                });
              }
            });

          } catch (error) {
            console.error('数据恢复失败:', error);
            wx.hideLoading();
            wx.showModal({
              title: '恢复失败',
              content: '备份数据格式错误或数据损坏，请检查后重试',
              showCancel: false,
              confirmText: '知道了'
            });
          }
        }
      }
    });
  },

  /**
   * AI配置管理
   */
  async manageAIConfig() {
    wx.showActionSheet({
      itemList: ['查看当前配置', '修改API密钥', '测试连接', '重置配置'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.showCurrentAIConfig();
            break;
          case 1:
            this.editAPIKey();
            break;
          case 2:
            this.testAIConnection();
            break;
          case 3:
            this.resetAIConfig();
            break;
        }
      }
    });
  },

  /**
   * 显示当前AI配置
   */
  showCurrentAIConfig() {
    const aiConfig = wx.getStorageSync('aiConfig') || {
      provider: 'doubao',
      apiKey: '未设置',
      model: 'doubao-lite-4k',
      temperature: 0.7
    };

    const configText = `当前AI配置：
提供商：${aiConfig.provider === 'doubao' ? '豆包AI' : aiConfig.provider}
模型：${aiConfig.model}
温度值：${aiConfig.temperature}
API密钥：${aiConfig.apiKey === '未设置' ? '未设置' : '已设置'}`;

    wx.showModal({
      title: 'AI配置信息',
      content: configText,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 编辑API密钥
   */
  editAPIKey() {
    wx.showModal({
      title: '设置API密钥',
      content: '请输入豆包AI的API密钥',
      editable: true,
      placeholderText: '请输入API密钥...',
      success: (res) => {
        if (res.confirm && res.content) {
          const aiConfig = wx.getStorageSync('aiConfig') || {};
          aiConfig.apiKey = res.content;
          wx.setStorageSync('aiConfig', aiConfig);

          wx.showToast({
            title: 'API密钥已保存',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 测试AI连接
   */
  async testAIConnection() {
    wx.showLoading({
      title: '测试连接中...',
      mask: true
    });

    try {
      const aiConfig = wx.getStorageSync('aiConfig');
      if (!aiConfig || !aiConfig.apiKey || aiConfig.apiKey === '未设置') {
        wx.hideLoading();
        wx.showModal({
          title: '配置错误',
          content: '请先设置API密钥',
          showCancel: false
        });
        return;
      }

      // 发送测试请求
      const testPrompt = '请回复"连接测试成功"';
      // 这里应该调用实际的AI服务
      // const result = await this.callAIService(testPrompt);

      // 模拟测试结果
      setTimeout(() => {
        wx.hideLoading();
        wx.showModal({
          title: '连接测试',
          content: 'AI服务连接正常，可以正常使用',
          showCancel: false,
          confirmText: '知道了'
        });
      }, 2000);

    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '连接失败',
        content: '无法连接到AI服务，请检查网络和配置',
        showCancel: false
      });
    }
  },

  /**
   * 重置AI配置
   */
  resetAIConfig() {
    wx.showModal({
      title: '重置配置',
      content: '确定要重置AI配置吗？这将清除所有自定义设置',
      success: (res) => {
        if (res.confirm) {
          const defaultConfig = {
            provider: 'doubao',
            apiKey: '未设置',
            model: 'doubao-lite-4k',
            temperature: 0.7,
            maxTokens: 1000
          };

          wx.setStorageSync('aiConfig', defaultConfig);
          wx.showToast({
            title: '配置已重置',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 跳转到设置
   */
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  /**
   * 退出登录
   */
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      confirmText: '退出',
      confirmColor: '#FF5247',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的用户信息
          wx.removeStorageSync('userInfo');
          wx.removeStorageSync('token');

          wx.showToast({
            title: '已退出登录',
            icon: 'success',
            duration: 1500
          });

          // 延迟跳转到登录页
          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }, 1500);
        }
      }
    });
  }
});