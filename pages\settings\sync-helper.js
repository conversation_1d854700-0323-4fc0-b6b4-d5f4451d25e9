/**
 * 数据同步助手 - 解决开发工具和手机端数据不同步问题
 */

class DataSyncHelper {
  constructor() {
    this.cloudSyncKey = 'user_profile_sync';
  }

  /**
   * 保存用户信息到云端
   */
  async saveToCloud(userInfo) {
    try {

      // 使用云开发数据库保存
      const db = wx.cloud.database();
      const result = await db.collection('user_profiles').add({
        data: {
          openid: '{openid}', // 云函数会自动替换
          userInfo: userInfo,
          updateTime: new Date(),
          platform: wx.getSystemInfoSync().platform,
          version: '3.0.0'
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('[同步助手] 云端保存失败:', error);
      
      // 降级方案：使用云函数
      try {
        const cloudResult = await wx.cloud.callFunction({
          name: 'saveUserProfile',
          data: { userInfo: userInfo }
        });

        return { success: true, data: cloudResult.result };
      } catch (cloudError) {
        console.error('[同步助手] 云函数保存也失败:', cloudError);
        return { success: false, error: cloudError };
      }
    }
  }

  /**
   * 从云端获取用户信息
   */
  async loadFromCloud() {
    try {

      // 使用云开发数据库查询
      const db = wx.cloud.database();
      const result = await db.collection('user_profiles')
        .where({
          openid: '{openid}' // 云函数会自动替换
        })
        .orderBy('updateTime', 'desc')
        .limit(1)
        .get();
      
      if (result.data && result.data.length > 0) {
        const userData = result.data[0];

        return { success: true, data: userData.userInfo };
      } else {

        return { success: false, error: '暂无云端数据' };
      }
    } catch (error) {
      console.error('[同步助手] 云端获取失败:', error);
      
      // 降级方案：使用云函数
      try {
        const cloudResult = await wx.cloud.callFunction({
          name: 'getUserProfile',
          data: {}
        });
        
        if (cloudResult.result && cloudResult.result.success) {

          return { success: true, data: cloudResult.result.data };
        } else {
          return { success: false, error: '云函数无数据' };
        }
      } catch (cloudError) {
        console.error('[同步助手] 云函数获取也失败:', cloudError);
        return { success: false, error: cloudError };
      }
    }
  }

  /**
   * 完整的同步流程：保存到本地 + 云端
   */
  async syncUserInfo(userInfo) {
    try {
      // 1. 立即保存到本地存储
      wx.setStorageSync('userInfo', userInfo);

      // 2. 更新全局数据
      const app = getApp();
      app.globalData.userInfo = userInfo;

      // 3. 异步保存到云端（不阻塞用户操作）
      this.saveToCloud(userInfo).then(result => {
        if (result.success) {

          // 显示成功提示（可选）
          wx.showToast({
            title: '信息已同步',
            icon: 'success',
            duration: 1500
          });
        } else {

        }
      });
      
      return { success: true };
    } catch (error) {
      console.error('[同步助手] 同步流程失败:', error);
      return { success: false, error: error };
    }
  }

  /**
   * 页面加载时的数据恢复
   */
  async restoreUserInfo() {
    try {
      // 1. 优先从本地获取
      const localData = wx.getStorageSync('userInfo');
      if (localData && (localData.name || localData.nickName)) {

        return { success: true, data: localData, source: 'local' };
      }
      
      // 2. 本地无数据时，从云端获取

      const cloudResult = await this.loadFromCloud();
      
      if (cloudResult.success) {
        // 云端数据恢复到本地
        wx.setStorageSync('userInfo', cloudResult.data);
        getApp().globalData.userInfo = cloudResult.data;

        return { success: true, data: cloudResult.data, source: 'cloud' };
      }
      
      // 3. 都没有数据，返回默认值
      const defaultInfo = {
        name: '未设置姓名',
        nickName: '老师',
        avatarUrl: ''
      };
      
      return { success: true, data: defaultInfo, source: 'default' };
    } catch (error) {
      console.error('[同步助手] 数据恢复失败:', error);
      return { success: false, error: error };
    }
  }
}

module.exports = {
  DataSyncHelper
};