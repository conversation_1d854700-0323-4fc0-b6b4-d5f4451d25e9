/**
 * 数据事件总线
 * 解决数据实时连通性问题，确保所有页面数据状态同步
 */

class DataEventBus {
  constructor() {
    this.listeners = new Map();
    this.dataCache = new Map();
    this.lastUpdateTime = new Map();

  }

  /**
   * 订阅数据变更事件
   * @param {string} dataType - 数据类型 (students, classes, comments, records)
   * @param {function} callback - 回调函数
   * @param {string} pageId - 页面标识
   */
  subscribe(dataType, callback, pageId = 'unknown') {
    if (!this.listeners.has(dataType)) {
      this.listeners.set(dataType, []);
    }
    
    const listener = {
      callback,
      pageId,
      subscribeTime: Date.now()
    };
    
    this.listeners.get(dataType).push(listener);

    // 如果有缓存数据，立即通知
    if (this.dataCache.has(dataType)) {
      const cachedData = this.dataCache.get(dataType);
      setTimeout(() => {
        callback(cachedData, 'cache');
      }, 0);
    }
    
    // 返回取消订阅函数
    return () => {
      this.unsubscribe(dataType, listener);
    };
  }

  /**
   * 取消订阅
   */
  unsubscribe(dataType, listener) {
    const listeners = this.listeners.get(dataType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);

      }
    }
  }

  /**
   * 发布数据变更事件
   * @param {string} dataType - 数据类型
   * @param {any} data - 新数据
   * @param {string} operation - 操作类型 (add, update, delete, clear, refresh)
   * @param {string} source - 数据来源页面
   */
  publish(dataType, data, operation = 'update', source = 'unknown') {
    
    // 更新缓存
    this.dataCache.set(dataType, data);
    this.lastUpdateTime.set(dataType, Date.now());
    
    // 通知所有订阅者
    const listeners = this.listeners.get(dataType) || [];
    listeners.forEach(listener => {
      try {
        // 通知所有订阅者，包括发起变更的页面（对于某些操作，发起页面也需要更新）
        // 只有在明确指定跳过的情况下才不通知
        const shouldNotify = operation === 'refresh' || operation === 'clear' || listener.pageId !== source;
        
        if (shouldNotify) {
          listener.callback(data, operation, source);
        }
      } catch (error) {
        console.error(`📢 通知页面 ${listener.pageId} 失败:`, error);
      }
    });
    
    // 记录事件日志
    this.logEvent(dataType, operation, source, listeners.length);
  }

  /**
   * 获取缓存数据
   */
  getCachedData(dataType) {
    return this.dataCache.get(dataType);
  }

  /**
   * 清除缓存
   */
  clearCache(dataType = null) {
    if (dataType) {
      this.dataCache.delete(dataType);
      this.lastUpdateTime.delete(dataType);

    } else {
      this.dataCache.clear();
      this.lastUpdateTime.clear();

    }
  }

  /**
   * 获取订阅状态
   */
  getSubscriptionStatus() {
    const status = {};
    this.listeners.forEach((listeners, dataType) => {
      status[dataType] = {
        subscriberCount: listeners.length,
        subscribers: listeners.map(l => l.pageId),
        lastUpdateTime: this.lastUpdateTime.get(dataType),
        hasCachedData: this.dataCache.has(dataType)
      };
    });
    return status;
  }

  /**
   * 记录事件日志
   */
  logEvent(dataType, operation, source, listenerCount) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      dataType,
      operation,
      source,
      listenerCount,
      notified: listenerCount > 0
    };
    
    // 保存到本地存储（最多保留100条）
    try {
      const logs = wx.getStorageSync('dataEventLogs') || [];
      logs.unshift(logEntry);
      if (logs.length > 100) {
        logs.splice(100);
      }
      wx.setStorageSync('dataEventLogs', logs);
    } catch (error) {

    }
  }

}

// 创建全局单例
const dataEventBus = new DataEventBus();

// 导出实例和工具函数
module.exports = {
  dataEventBus,

  // 便捷方法
  subscribe: (dataType, callback, pageId) => dataEventBus.subscribe(dataType, callback, pageId),
  publish: (dataType, data, operation, source) => dataEventBus.publish(dataType, data, operation, source),
  getCachedData: (dataType) => dataEventBus.getCachedData(dataType),
  clearCache: (dataType) => dataEventBus.clearCache(dataType),
  getSubscriptionStatus: () => dataEventBus.getSubscriptionStatus()
};
