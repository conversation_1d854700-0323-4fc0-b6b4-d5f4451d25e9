/**
 * 成就徽章管理器
 * 负责徽章的解锁、检测和弹窗提示
 */

class AchievementManager {
  constructor() {
    this.achievements = this.getAchievementDefinitions();
    this.init();
  }

  /**
   * 初始化徽章系统
   */
  async init() {

    try {
      // 优先从云端加载徽章数据
      await this.loadFromCloud();
    } catch (error) {

      // 降级到本地存储
      this.loadFromLocal();
    }

  }

  /**
   * 从云端加载徽章数据
   */
  async loadFromCloud() {
    try {
      const cloudService = this.getCloudService();
      const result = await cloudService.getUserAchievements();

      if (result.success) {
        this.unlockedAchievements = result.data.achievements || [];
        this.cloudVersion = result.data.version || 0;
        this.lastCloudSync = Date.now();

        // 同步到本地缓存
        wx.setStorageSync('unlockedAchievements', this.unlockedAchievements);
        wx.setStorageSync('achievementCloudVersion', this.cloudVersion);
        wx.setStorageSync('lastAchievementSync', this.lastCloudSync);

        return true;
      } else {
        throw new Error(result.error || '云端数据加载失败');
      }
    } catch (error) {
      console.error('[徽章系统] 云端数据加载失败:', error);
      throw error;
    }
  }

  /**
   * 从本地存储加载徽章数据
   */
  loadFromLocal() {
    this.unlockedAchievements = wx.getStorageSync('unlockedAchievements') || [];
    this.cloudVersion = wx.getStorageSync('achievementCloudVersion') || 0;
    this.lastCloudSync = wx.getStorageSync('lastAchievementSync') || 0;

  }

  /**
   * 获取云服务实例
   */
  getCloudService() {
    try {
      const { getCloudService } = require('../services/cloudService');
      return getCloudService();
    } catch (error) {
      console.error('[徽章系统] 获取云服务失败:', error);
      throw error;
    }
  }

  /**
   * 获取徽章定义
   */
  getAchievementDefinitions() {
    return [
      {
        id: 'efficiency_master',
        name: '效率达人',
        description: '单日记录行为10条',
        icon: '🚀',
        type: 'daily',
        target: 10,
        checkFunction: 'checkDailyRecords'
      },
      {
        id: 'quality_expert', 
        name: '质量专家',
        description: '连续5次评语8分以上',
        icon: '⭐',
        type: 'streak',
        target: 5,
        checkFunction: 'checkQualityStreak'
      },
      {
        id: 'progress_mentor',
        name: '进步导师', 
        description: '累计记录100条行为记录',
        icon: '📚',
        type: 'cumulative',
        target: 100,
        checkFunction: 'checkTotalRecords'
      },
      {
        id: 'ai_education_expert',
        name: 'AI教育专家',
        description: '累计记录500条行为记录', 
        icon: '👑',
        type: 'cumulative',
        target: 500,
        checkFunction: 'checkTotalRecords'
      }
    ];
  }

  /**
   * 检查所有徽章状态
   */
  async checkAllAchievements() {
    const newlyUnlocked = [];
    
    for (const achievement of this.achievements) {
      if (!this.isUnlocked(achievement.id)) {
        const result = await this[achievement.checkFunction](achievement);
        if (result.achieved) {
          this.unlockAchievement(achievement);
          newlyUnlocked.push(achievement);
        }
      }
    }

    // 如果有新解锁的徽章，显示弹窗
    if (newlyUnlocked.length > 0) {
      this.showAchievementModal(newlyUnlocked);
    }

    return newlyUnlocked;
  }

  /**
   * 检查单日记录数
   */
  async checkDailyRecords(achievement) {
    try {
      const today = new Date().toDateString();

      // 从云数据库获取记录数据
      const cloudService = this.getCloudService();
      const recordResult = await cloudService.getRecordList({ pageSize: 1000 });

      // 确保records是数组
      let records = [];
      if (recordResult.success && recordResult.data) {
        if (Array.isArray(recordResult.data)) {
          records = recordResult.data;
        } else if (Array.isArray(recordResult.data.records)) {
          records = recordResult.data.records;
        } else if (Array.isArray(recordResult.data.data)) {
          records = recordResult.data.data;
        }
      }

      const todayRecords = records.filter(record => {
        const recordDate = new Date(record.createTime || Date.now()).toDateString();
        return recordDate === today;
      });

      const current = todayRecords.length;
      const progress = Math.min(100, (current / achievement.target) * 100);

      return {
        achieved: current >= achievement.target,
        current,
        target: achievement.target,
        progress
      };
    } catch (error) {
      console.error('检查单日记录失败:', error);
      return { achieved: false, current: 0, target: achievement.target, progress: 0 };
    }
  }

  /**
   * 检查质量连续记录
   */
  async checkQualityStreak(achievement) {
    try {
      // 从云数据库获取评语数据
      const cloudService = this.getCloudService();
      const commentResult = await cloudService.getCommentList({ pageSize: 1000 });

      // 确保comments是数组
      let comments = [];
      if (commentResult.success && commentResult.data) {
        if (Array.isArray(commentResult.data)) {
          comments = commentResult.data;
        } else if (Array.isArray(commentResult.data.comments)) {
          comments = commentResult.data.comments;
        } else if (Array.isArray(commentResult.data.data)) {
          comments = commentResult.data.data;
        }
      }

      // 确保 comments 是数组
      if (!Array.isArray(comments)) {

        comments = [];
      }

      // 按时间排序，检查最近的评语
      const sortedComments = comments.sort((a, b) => {
        const timeA = new Date(a.createTime || a.generateTime || 0).getTime();
        const timeB = new Date(b.createTime || b.generateTime || 0).getTime();
        return timeB - timeA;
      });

      let streak = 0;
      for (const comment of sortedComments) {
        const score = comment.score || 0;
        if (score >= 8.0) {
          streak++;
        } else {
          break; // 连续记录中断
        }
      }

      const progress = Math.min(100, (streak / achievement.target) * 100);

      return {
        achieved: streak >= achievement.target,
        current: streak,
        target: achievement.target,
        progress
      };
    } catch (error) {
      console.error('检查质量连续记录失败:', error);
      return { achieved: false, current: 0, target: achievement.target, progress: 0 };
    }
  }

  /**
   * 检查累计记录数
   */
  async checkTotalRecords(achievement) {
    try {
      // 从云数据库获取记录数据
      const cloudService = this.getCloudService();
      const recordResult = await cloudService.getRecordList({ pageSize: 1000 });

      // 确保records是数组
      let records = [];
      if (recordResult.success && recordResult.data) {
        if (Array.isArray(recordResult.data)) {
          records = recordResult.data;
        } else if (Array.isArray(recordResult.data.records)) {
          records = recordResult.data.records;
        } else if (Array.isArray(recordResult.data.data)) {
          records = recordResult.data.data;
        }
      }

      const current = records.length;
      const progress = Math.min(100, (current / achievement.target) * 100);

      return {
        achieved: current >= achievement.target,
        current,
        target: achievement.target,
        progress
      };
    } catch (error) {
      console.error('检查累计记录失败:', error);
      return { achieved: false, current: 0, target: achievement.target, progress: 0 };
    }
  }

  /**
   * 解锁徽章（支持云端同步）
   */
  async unlockAchievement(achievement) {
    if (!this.isUnlocked(achievement.id)) {
      const newAchievement = {
        id: achievement.id,
        unlockedAt: Date.now(),
        ...achievement
      };

      // 添加到本地数组
      this.unlockedAchievements.push(newAchievement);

      // 立即保存到本地存储
      wx.setStorageSync('unlockedAchievements', this.unlockedAchievements);

      // 异步同步到云端
      this.syncToCloud().catch(error => {

      });

      return newAchievement;
    }
    return null;
  }

  /**
   * 同步到云端
   */
  async syncToCloud() {
    try {
      const cloudService = this.getCloudService();
      const result = await cloudService.updateUserAchievements(this.unlockedAchievements);

      if (result.success) {
        this.cloudVersion = result.data.version;
        this.lastCloudSync = Date.now();

        // 更新本地缓存的版本信息
        wx.setStorageSync('achievementCloudVersion', this.cloudVersion);
        wx.setStorageSync('lastAchievementSync', this.lastCloudSync);

        return true;
      } else {
        throw new Error(result.error || '云端同步失败');
      }
    } catch (error) {
      console.error('[徽章系统] 云端同步失败:', error);
      throw error;
    }
  }

  /**
   * 检查徽章是否已解锁
   */
  isUnlocked(achievementId) {
    return this.unlockedAchievements.some(item => item.id === achievementId);
  }

  /**
   * 显示徽章解锁弹窗
   */
  showAchievementModal(newAchievements) {
    if (newAchievements.length === 1) {
      const achievement = newAchievements[0];
      wx.showModal({
        title: '🎉 恭喜解锁新徽章！',
        content: `━━━━━━━━━━━━━━━━━━━━━━━━
${achievement.icon} ${achievement.name}

📋 解锁条件：
${achievement.description}

🎯 成就意义：
${this.getAchievementMeaning(achievement.id)}

💡 继续努力，解锁更多徽章！
━━━━━━━━━━━━━━━━━━━━━━━━`,
        confirmText: '太棒了！',
        cancelText: '查看所有徽章',
        success: (res) => {
          if (!res.confirm) {
            // 跳转到成长报告页面查看徽章
            this.navigateToAchievements();
          }
        }
      });
    } else {
      // 多个徽章同时解锁
      const achievementList = newAchievements.map(a => `${a.icon} ${a.name}`).join('\n');
      wx.showModal({
        title: '🌟 一次解锁多个徽章！',
        content: `━━━━━━━━━━━━━━━━━━━━━━━━
🎉 恭喜您解锁了 ${newAchievements.length} 个徽章：

${achievementList}

🏆 您的努力得到了认可！
继续加油，成为AI教育专家！
━━━━━━━━━━━━━━━━━━━━━━━━`,
        confirmText: '查看详情',
        cancelText: '继续使用',
        success: (res) => {
          if (res.confirm) {
            this.navigateToAchievements();
          }
        }
      });
    }
  }

  /**
   * 获取徽章意义说明
   */
  getAchievementMeaning(achievementId) {
    const meanings = {
      efficiency_master: '证明您已经熟练掌握了行为记录的技巧，能够高效地捕捉学生的日常表现',
      quality_expert: '展现了您对评语质量的精益求精，每一条评语都体现了专业水准',
      progress_mentor: '标志着您已经积累了丰富的教学观察经验，成为学生成长的见证者',
      ai_education_expert: '您已经成为AI辅助教育的资深用户，引领教育技术的创新应用'
    };
    return meanings[achievementId] || '恭喜您取得这项成就！';
  }

  /**
   * 跳转到徽章页面
   */
  navigateToAchievements() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.route !== 'pages/analytics/report/report') {
      wx.navigateTo({
        url: '/pages/analytics/report/report'
      });
    }
  }

  /**
   * 获取所有徽章状态（用于报告页面显示）
   */
  async getAllAchievementStatus() {
    const achievementStatus = [];
    
    for (const achievement of this.achievements) {
      const result = await this[achievement.checkFunction](achievement);
      const isUnlocked = this.isUnlocked(achievement.id);
      
      achievementStatus.push({
        ...achievement,
        ...result,
        isUnlocked,
        unlockedAt: isUnlocked ? this.unlockedAchievements.find(a => a.id === achievement.id).unlockedAt : null
      });
    }

    return achievementStatus;
  }

  /**
   * 手动触发徽章检查（在关键操作后调用）
   */
  async triggerAchievementCheck() {

    return await this.checkAllAchievements();
  }

  /**
   * 清空所有徽章数据（支持云端清空）
   */
  async clearAllAchievements() {
    try {

      // 清空云端数据
      try {
        const cloudService = this.getCloudService();
        const cloudResult = await cloudService.clearUserAchievements();

        if (cloudResult.success) {

        } else {

        }
      } catch (error) {

      }

      // 清空本地数据
      this.unlockedAchievements = [];
      this.cloudVersion = 0;
      this.lastCloudSync = 0;

      // 清空本地存储
      wx.removeStorageSync('unlockedAchievements');
      wx.removeStorageSync('achievementCloudVersion');
      wx.removeStorageSync('lastAchievementSync');
      wx.removeStorageSync('achievementProgress');
      wx.removeStorageSync('achievementCache');

      return {
        success: true,
        message: '徽章数据清空完成'
      };
    } catch (error) {
      console.error('[徽章系统] 清空徽章数据失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 强制从云端重新同步数据
   */
  async forceSync() {
    try {

      const cloudService = this.getCloudService();
      const localVersion = this.cloudVersion || 0;

      const syncResult = await cloudService.syncAchievements(this.unlockedAchievements, localVersion);

      if (syncResult.success) {
        this.unlockedAchievements = syncResult.data.achievements;
        this.cloudVersion = syncResult.data.version;
        this.lastCloudSync = Date.now();

        // 更新本地缓存
        wx.setStorageSync('unlockedAchievements', this.unlockedAchievements);
        wx.setStorageSync('achievementCloudVersion', this.cloudVersion);
        wx.setStorageSync('lastAchievementSync', this.lastCloudSync);

        return syncResult;
      } else {
        throw new Error(syncResult.error);
      }
    } catch (error) {
      console.error('[徽章系统] 强制同步失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取徽章统计
   */
  getAchievementStats() {
    const total = this.achievements.length;
    const unlocked = this.unlockedAchievements.length;
    const percentage = total > 0 ? Math.round((unlocked / total) * 100) : 0;

    return {
      total,
      unlocked,
      locked: total - unlocked,
      percentage
    };
  }
}

// 创建全局实例
const achievementManager = new AchievementManager();

module.exports = {
  AchievementManager,
  achievementManager
};