/**
 * 云端徽章系统演示和测试工具
 * 展示如何使用新的云端徽章系统
 */

class CloudAchievementDemo {
  constructor() {
    this.achievementManager = null;
  }

  /**
   * 初始化演示
   */
  async init() {
    try {
      const AchievementManager = require('./achievementManager');
      this.achievementManager = new AchievementManager();
      await this.achievementManager.init();

      return true;
    } catch (error) {
      console.error('演示初始化失败:', error);
      return false;
    }
  }

  /**
   * 演示1：查看当前徽章状态
   */
  async demo1_checkCurrentStatus() {

    try {
      const status = await this.achievementManager.getAllAchievementStatus();

      status.forEach(achievement => {
        const statusIcon = achievement.isUnlocked ? '✅' : '⏳';
        const progress = achievement.progress ? `${achievement.progress}%` : '0%';
        
      });
      
      return status;
    } catch (error) {
      console.error('查看徽章状态失败:', error);
      return [];
    }
  }

  /**
   * 演示2：模拟解锁徽章
   */
  async demo2_unlockAchievement() {

    try {
      // 模拟解锁"效率达人"徽章
      const achievement = {
        id: 'efficiency_master',
        name: '效率达人',
        description: '单日记录行为10条',
        icon: '🚀'
      };
      
      const result = await this.achievementManager.unlockAchievement(achievement);
      
      if (result) {

        // 显示解锁提示
        if (typeof wx !== 'undefined' && wx.showToast) {
          wx.showToast({
            title: `🎉 解锁 ${result.name}`,
            icon: 'success',
            duration: 2000
          });
        }
        
        return result;
      } else {

        return null;
      }
    } catch (error) {
      console.error('解锁徽章失败:', error);
      return null;
    }
  }

  /**
   * 演示3：云端数据同步
   */
  async demo3_cloudSync() {

    try {
      const syncResult = await this.achievementManager.forceSync();
      
      if (syncResult.success) {

        return syncResult.data;
      } else {
        console.error('❌ 云端同步失败:', syncResult.error);
        return null;
      }
    } catch (error) {
      console.error('云端同步异常:', error);
      return null;
    }
  }

  /**
   * 演示4：清空徽章数据
   */
  async demo4_clearData() {

    try {
      // 先显示清空前的状态
      const beforeStatus = await this.achievementManager.getAllAchievementStatus();
      const unlockedBefore = beforeStatus.filter(a => a.isUnlocked).length;

      // 执行清空
      const clearResult = await this.achievementManager.clearAllAchievements();
      
      if (clearResult.success) {

        // 显示清空后的状态
        const afterStatus = await this.achievementManager.getAllAchievementStatus();
        const unlockedAfter = afterStatus.filter(a => a.isUnlocked).length;

        if (typeof wx !== 'undefined' && wx.showToast) {
          wx.showToast({
            title: '徽章数据已清空',
            icon: 'success'
          });
        }
        
        return clearResult;
      } else {
        console.error('❌ 徽章数据清空失败:', clearResult.error);
        return null;
      }
    } catch (error) {
      console.error('清空徽章数据异常:', error);
      return null;
    }
  }

  /**
   * 演示5：完整的徽章生命周期
   */
  async demo5_fullLifecycle() {

    try {

      await this.demo4_clearData();

      await this.demo1_checkCurrentStatus();

      await this.demo2_unlockAchievement();

      await this.demo3_cloudSync();

      await this.demo1_checkCurrentStatus();

      return true;
    } catch (error) {
      console.error('完整生命周期演示失败:', error);
      return false;
    }
  }

  /**
   * 运行所有演示
   */
  async runAllDemos() {

    const initSuccess = await this.init();
    if (!initSuccess) {
      console.error('❌ 初始化失败，无法运行演示');
      return false;
    }
    
    try {
      await this.demo1_checkCurrentStatus();
      await this.demo2_unlockAchievement();
      await this.demo3_cloudSync();
      // await this.demo4_clearData(); // 注释掉清空演示，避免意外清空数据

      return true;
    } catch (error) {
      console.error('演示运行失败:', error);
      return false;
    }
  }
}

// 导出演示类
module.exports = {
  CloudAchievementDemo
};

// 全局函数，方便在控制台中调用
if (typeof global !== 'undefined') {
  global.runCloudAchievementDemo = async function() {
    const demo = new CloudAchievementDemo();
    return await demo.runAllDemos();
  };
  
  global.testCloudAchievement = async function(action = 'status') {
    const demo = new CloudAchievementDemo();
    await demo.init();
    
    switch (action) {
      case 'status':
        return await demo.demo1_checkCurrentStatus();
      case 'unlock':
        return await demo.demo2_unlockAchievement();
      case 'sync':
        return await demo.demo3_cloudSync();
      case 'clear':
        return await demo.demo4_clearData();
      case 'full':
        return await demo.demo5_fullLifecycle();
      default:

        return null;
    }
  };
}

