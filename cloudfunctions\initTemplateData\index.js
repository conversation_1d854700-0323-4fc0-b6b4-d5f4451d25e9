/**
 * 模板数据初始化云函数
 * Ultra-Think架构 - 确保数据库中有完整的模板数据
 */

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

/**
 * 云函数入口
 */
exports.main = async (event, context) => {
  console.log('开始初始化模板数据...')
  
  try {
    // 检查是否已有模板数据
    const existingTemplates = await db.collection('prompt_templates').get()
    
    if (existingTemplates.data.length > 0) {
      console.log(`发现已有${existingTemplates.data.length}个模板，跳过初始化`)
      return {
        success: true,
        message: '模板数据已存在，跳过初始化',
        existingCount: existingTemplates.data.length
      }
    }

    // 定义完整的默认模板数据（与用户原始专业版本一致）
    const DEFAULT_TEMPLATES = [
      {
        id: 'formal_v1',
        name: '综合评价型',
        type: 'formal',
        category: 'term',
        content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的资深班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份全面、客观、个性化、充满关怀的学期综合评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"总体评价 + 优点详述 + 待改进点与期望 + 结尾祝福"的结构。
2. **内容要求**：优点详述部分必须从素材中提炼2-3个最突出的优点，并引用具体事例来支撑
3. **语气风格**：语言要真诚、平实、温暖，体现中职老师的专业性和人文关怀
4. **个性化**：必须尊重、保留且紧密结合提供的素材，体现学生独特性
5. **缺失处理**：当表现素材为空，则只输出"无行为依据，无法生评语！"

请直接生成完整的评语内容，100-150字之间。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名', placeholder: '请输入学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料', placeholder: '请输入学生的具体表现情况' }
        ],
        metadata: {
          author: 'system',
          description: '全面、客观、个性化的综合评价模板',
          tags: ['formal', 'comprehensive', 'term'],
          usageCount: 0
        },
        version: 1,
        enabled: true,
        timestamps: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      },
      {
        id: 'warm_v1',
        name: '优点鼓励型',
        type: 'warm',
        category: 'term',
        content: `你是一位善于发现学生闪光点的温暖班主任。你的任务是根据学生日常表现素材，撰写一份重点突出优点、充满鼓励的正向评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

撰写要求：
1. **聚焦优点**：从素材中挖掘至少3个具体优点，用鼓励性语言表达
2. **具体事例**：每个优点都要有具体的行为事例支撑，让表扬有据可依
3. **情感温度**：语言要充满温暖和欣赏，让学生感受到被肯定的力量
4. **未来导向**：在肯定现有表现的基础上，表达对学生未来发展的信心
5. **缺失处理**：素材为空时输出"无行为依据，无法生评语！"

请生成80-120字的鼓励型评语。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名', placeholder: '请输入学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料', placeholder: '请输入学生的具体表现情况' }
        ],
        metadata: {
          author: 'system',
          description: '重点突出优点、充满鼓励的正向评语模板',
          tags: ['warm', 'encouraging', 'positive'],
          usageCount: 0
        },
        version: 1,
        enabled: true,
        timestamps: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      },
      {
        id: 'encouraging_v1',
        name: '改进建议型',
        type: 'encouraging',
        category: 'term',
        content: `你是一位经验丰富的教育导师，擅长以建设性方式指导学生成长。请根据学生表现素材，撰写一份侧重改进建议的发展性评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

撰写要求：
1. **平衡表述**：先肯定1-2个优点，再委婉指出需要改进的方面
2. **具体建议**：针对改进点提供具体、可操作的建议和方法
3. **鼓励语调**：用"希望看到"、"相信你能"等积极语言表达期望
4. **成长导向**：将改进点表述为成长机会，而非批评
5. **缺失处理**：素材为空时输出"无行为依据，无法生评语！"

请生成100-140字的发展性评语。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名', placeholder: '请输入学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料', placeholder: '请输入学生的具体表现情况' }
        ],
        metadata: {
          author: 'system',
          description: '侧重改进建议的发展性评语模板',
          tags: ['encouraging', 'developmental', 'constructive'],
          usageCount: 0
        },
        version: 1,
        enabled: true,
        timestamps: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      },
      {
        id: 'detailed_v1',
        name: '成长记录型',
        type: 'detailed',
        category: 'term',
        content: `你是一位注重记录学生成长轨迹的细心班主任。请根据学生表现素材，撰写一份侧重成长变化的阶段性评语。

学生姓名：<student_name>{{ }}</student_name>
学生本学期的日常表现素材如下：
<performance_material>{{ }}</performance_material>

撰写要求：
1. **时间维度**：体现学生在本学期的变化和进步轨迹
2. **对比描述**：如果素材中有前后对比，要突出变化过程
3. **具体记录**：引用具体的时间节点和事件，增强真实感
4. **成长价值**：强调每一个小进步的意义和价值
5. **未来展望**：基于当前进步趋势，对下阶段发展给出期待
6. **缺失处理**：素材为空时输出"无行为依据，无法生评语！"

请生成110-160字的成长记录型评语。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名', placeholder: '请输入学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料', placeholder: '请输入学生的具体表现情况' }
        ],
        metadata: {
          author: 'system',
          description: '侧重成长变化的阶段性评语模板',
          tags: ['detailed', 'growth', 'progressive'],
          usageCount: 0
        },
        version: 1,
        enabled: true,
        timestamps: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
    ]

    // 生成校验和（内联函数避免依赖问题）
    const generateChecksum = (content) => {
      // 简单的内容校验和生成
      let hash = 0
      if (content.length === 0) return hash.toString()
      for (let i = 0; i < content.length; i++) {
        const char = content.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash // 转换为32位整数
      }
      return Math.abs(hash).toString()
    }

    const processedTemplates = DEFAULT_TEMPLATES.map(template => ({
      ...template,
      checksum: generateChecksum(template.content),
      createTime: db.serverDate(),
      updateTime: db.serverDate(),
      createTimestamp: Date.now(),
      updateTimestamp: Date.now()
    }))

    console.log('准备插入模板数据:', processedTemplates.length)

    processedTemplates.forEach(template => {
      console.log(`模板: ${template.name}, content长度: ${template.content?.length || 0}`)
      console.log(`模板内容预览: ${template.content?.substring(0, 100) || '内容为空'}...`)
    })

    // 批量插入模板数据
    const insertPromises = processedTemplates.map(async (template) => {
      try {
        console.log(`开始插入模板: ${template.name}`)
        const result = await db.collection('prompt_templates').add({
          data: template
        })
        console.log(`模板插入成功: ${template.name} (${result._id})`)
        return {
          success: true,
          template: template.name,
          id: result._id
        }
      } catch (error) {
        console.error(`模板插入失败: ${template.name}`, error)
        return {
          success: false,
          template: template.name,
          error: error.message
        }
      }
    })

    const insertResults = await Promise.all(insertPromises)
    const successCount = insertResults.filter(r => r.success).length
    const failCount = insertResults.filter(r => !r.success).length

    console.log(`模板初始化完成: 成功${successCount}个, 失败${failCount}个`)

    // 验证插入结果
    const finalCount = await db.collection('prompt_templates').count()
    console.log(`数据库中模板总数: ${finalCount.total}`)

    return {
      success: true,
      message: `模板数据初始化成功`,
      data: {
        insertedCount: successCount,
        failedCount: failCount,
        totalCount: finalCount.total,
        insertResults: insertResults
      }
    }

  } catch (error) {
    console.error('模板数据初始化失败:', error)
    return {
      success: false,
      error: error.message,
      message: '模板数据初始化失败，请检查系统配置'
    }
  }
}