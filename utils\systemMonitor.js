/**
 * 系统状态监控工具
 * 监控前后端连通性、数据同步状态、性能指标等
 */

class SystemMonitor {
  constructor() {
    this.monitoringEnabled = false;
    this.monitoringInterval = null;
    this.statusHistory = [];
    this.alertThresholds = {
      responseTime: 5000, // 5秒响应时间阈值
      errorRate: 0.1,     // 10%错误率阈值
      memoryUsage: 100,   // 100MB内存使用阈值
      syncDelay: 30000    // 30秒同步延迟阈值
    };
    
    // 延迟初始化，避免在页面加载时立即启动
    setTimeout(() => {
      this.init();
    }, 5000);
  }

  /**
   * 初始化监控系统
   */
  init() {
    // 暂时不自动启动监控，避免影响页面加载性能
    // this.startMonitoring();
    
    // 监听应用生命周期
    this.setupAppLifecycleMonitoring();
    
    // 监听网络状态变化
    this.setupNetworkMonitoring();
  }

  /**
   * 启动定期监控
   */
  startMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    // 每30秒执行一次系统检查
    this.monitoringInterval = setInterval(async () => {
      if (this.monitoringEnabled) {
        await this.performSystemCheck();
      }
    }, 30000);
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.monitoringEnabled = false;
  }

  /**
   * 执行系统检查
   */
  async performSystemCheck() {
    const checkStartTime = Date.now();
    const status = {
      timestamp: new Date().toISOString(),
      checks: {}
    };

    try {
      // 检查云服务连接
      status.checks.cloudService = await this.checkCloudService();
      
      // 检查数据库连接
      status.checks.database = await this.checkDatabaseConnection();
      
      // 检查实时同步状态
      status.checks.realTimeSync = await this.checkRealTimeSync();
      
      // 检查本地存储状态
      status.checks.localStorage = await this.checkLocalStorage();
      
      // 检查网络状态
      status.checks.network = await this.checkNetworkStatus();
      
      // 检查内存使用
      status.checks.memory = await this.checkMemoryUsage();
      
      // 检查增长功能状态
      status.checks.growthFeatures = await this.checkGrowthFeatures();

      // 计算总体健康状态
      status.overallHealth = this.calculateOverallHealth(status.checks);
      status.checkDuration = Date.now() - checkStartTime;

      // 保存状态历史
      this.saveStatusHistory(status);
      
      // 检查是否需要告警
      this.checkAlerts(status);
    } catch (error) {
      console.error('[SystemMonitor] 系统检查失败:', error);
      status.error = error.message;
      status.overallHealth = 'error';
    }

    return status;
  }

  /**
   * 检查云服务连接
   */
  async checkCloudService() {
    const startTime = Date.now();
    
    try {
      const app = getApp();
      const cloudService = app.globalData.cloudService;
      
      if (!cloudService) {
        return {
          status: 'unavailable',
          message: '云服务未初始化',
          responseTime: Date.now() - startTime
        };
      }

      await cloudService.testConnection();
      
      return {
        status: 'healthy',
        message: '云服务连接正常',
        responseTime: Date.now() - startTime
      };

    } catch (error) {
      return {
        status: 'error',
        message: `云服务连接失败: ${error.message}`,
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 检查数据库连接
   */
  async checkDatabaseConnection() {
    const startTime = Date.now();
    
    try {
      const app = getApp();
      const dbManager = app.globalData.databaseConnectionManager;
      
      if (!dbManager) {
        return {
          status: 'unavailable',
          message: '数据库连接管理器未初始化',
          responseTime: Date.now() - startTime
        };
      }

      // 获取连接统计
      const stats = dbManager.getConnectionStats();
      
      const healthyRatio = stats.totalConnections > 0 ? 
        stats.healthyConnections / stats.totalConnections : 0;

      let status = 'healthy';
      let message = `数据库连接正常 (${stats.healthyConnections}/${stats.totalConnections})`;

      if (healthyRatio < 0.8) {
        status = 'warning';
        message = `数据库连接异常 (${stats.healthyConnections}/${stats.totalConnections})`;
      }

      if (stats.totalConnections === 0) {
        status = 'error';
        message = '无可用数据库连接';
      }

      return {
        status,
        message,
        responseTime: Date.now() - startTime,
        connectionStats: stats
      };

    } catch (error) {
      return {
        status: 'error',
        message: `数据库检查失败: ${error.message}`,
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 检查实时同步状态
   */
  async checkRealTimeSync() {
    const startTime = Date.now();
    
    try {
      const app = getApp();
      const syncManager = app.globalData.realTimeSyncManager;
      
      if (!syncManager) {
        return {
          status: 'unavailable',
          message: '实时同步管理器未初始化',
          responseTime: Date.now() - startTime
        };
      }

      // 检查同步队列状态
      const queueLength = syncManager.syncQueue ? syncManager.syncQueue.length : 0;
      const isOnline = syncManager.isOnline;
      const syncInProgress = syncManager.syncInProgress;

      let status = 'healthy';
      let message = `同步状态正常 (队列: ${queueLength}项)`;

      if (!isOnline) {
        status = 'warning';
        message = '离线模式，数据将在网络恢复后同步';
      }

      if (queueLength > 50) {
        status = 'warning';
        message = `同步队列积压 (${queueLength}项)`;
      }

      if (syncInProgress) {
        message += ' [同步中]';
      }

      return {
        status,
        message,
        responseTime: Date.now() - startTime,
        queueLength,
        isOnline,
        syncInProgress
      };

    } catch (error) {
      return {
        status: 'error',
        message: `同步状态检查失败: ${error.message}`,
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 检查本地存储状态
   */
  async checkLocalStorage() {
    const startTime = Date.now();
    
    try {
      // 检查关键数据的存储状态
      const keyData = {
        userInfo: wx.getStorageSync('userInfo'),
        savedComments: wx.getStorageSync('savedComments'),
        recentComments: wx.getStorageSync('recentComments'),
        growthEvents: wx.getStorageSync('growth_events'),
        visitHistory: wx.getStorageSync('visit_history')
      };

      // 计算存储使用情况
      const storageInfo = wx.getStorageInfoSync();
      const usageRatio = storageInfo.currentSize / storageInfo.limitSize;

      let status = 'healthy';
      let message = `本地存储正常 (${storageInfo.currentSize}KB/${storageInfo.limitSize}KB)`;

      if (usageRatio > 0.8) {
        status = 'warning';
        message = `本地存储使用率过高 (${Math.round(usageRatio * 100)}%)`;
      }

      // 检查关键数据完整性
      const missingData = Object.entries(keyData)
        .filter(([key, value]) => !value)
        .map(([key]) => key);

      if (missingData.length > 0) {
        message += ` [缺失: ${missingData.join(', ')}]`;
      }

      return {
        status,
        message,
        responseTime: Date.now() - startTime,
        storageInfo,
        usageRatio,
        missingData
      };

    } catch (error) {
      return {
        status: 'error',
        message: `本地存储检查失败: ${error.message}`,
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 检查网络状态
   */
  async checkNetworkStatus() {
    const startTime = Date.now();
    
    try {
      const networkType = await new Promise((resolve, reject) => {
        wx.getNetworkType({
          success: (res) => resolve(res.networkType),
          fail: reject
        });
      });

      let status = 'healthy';
      let message = `网络连接正常 (${networkType})`;

      if (networkType === 'none') {
        status = 'error';
        message = '网络连接断开';
      } else if (networkType === '2g') {
        status = 'warning';
        message = '网络连接较慢 (2G)';
      }

      return {
        status,
        message,
        responseTime: Date.now() - startTime,
        networkType
      };

    } catch (error) {
      return {
        status: 'error',
        message: `网络状态检查失败: ${error.message}`,
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 检查内存使用
   */
  async checkMemoryUsage() {
    const startTime = Date.now();
    
    try {
      // 小程序环境下的内存检查相对简单
      const systemInfo = wx.getDeviceInfo();
      
      // 估算当前内存使用（基于存储数据大小）
      const storageInfo = wx.getStorageInfoSync();
      const estimatedMemory = storageInfo.currentSize * 2; // 粗略估算

      let status = 'healthy';
      let message = `内存使用正常 (~${estimatedMemory}KB)`;

      if (estimatedMemory > this.alertThresholds.memoryUsage * 1024) {
        status = 'warning';
        message = `内存使用较高 (~${estimatedMemory}KB)`;
      }

      return {
        status,
        message,
        responseTime: Date.now() - startTime,
        estimatedMemory,
        systemInfo: {
          platform: systemInfo.platform,
          system: systemInfo.system,
          version: systemInfo.version
        }
      };

    } catch (error) {
      return {
        status: 'error',
        message: `内存检查失败: ${error.message}`,
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 检查增长功能状态
   */
  async checkGrowthFeatures() {
    const startTime = Date.now();
    
    try {
      const { GROWTH_CONFIG } = require('./growthEngine');
      
      // 检查增长功能配置
      const enabledFeatures = Object.entries(GROWTH_CONFIG)
        .filter(([key, value]) => value === true)
        .map(([key]) => key);

      // 检查增长数据
      const growthData = {
        events: wx.getStorageSync('growth_events') || [],
        shareHistory: wx.getStorageSync('share_history') || [],
        visitHistory: wx.getStorageSync('visit_history') || [],
        freeCount: wx.getStorageSync('free_ai_count') || 0
      };

      let status = 'healthy';
      let message = `增长功能正常 (${enabledFeatures.length}个功能启用)`;

      if (!GROWTH_CONFIG.enabled) {
        status = 'warning';
        message = '增长功能已禁用';
      }

      return {
        status,
        message,
        responseTime: Date.now() - startTime,
        enabledFeatures,
        dataStats: {
          events: growthData.events.length,
          shares: growthData.shareHistory.length,
          visits: growthData.visitHistory.length,
          freeCount: growthData.freeCount
        }
      };

    } catch (error) {
      return {
        status: 'error',
        message: `增长功能检查失败: ${error.message}`,
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * 计算总体健康状态
   */
  calculateOverallHealth(checks) {
    const statuses = Object.values(checks).map(check => check.status);
    
    if (statuses.includes('error')) {
      return 'error';
    } else if (statuses.includes('warning')) {
      return 'warning';
    } else if (statuses.includes('unavailable')) {
      return 'degraded';
    } else {
      return 'healthy';
    }
  }

  /**
   * 保存状态历史
   */
  saveStatusHistory(status) {
    try {
      this.statusHistory.push(status);
      
      // 只保留最近50次检查记录
      if (this.statusHistory.length > 50) {
        this.statusHistory = this.statusHistory.slice(-50);
      }

      // 保存到本地存储
      wx.setStorageSync('system_status_history', this.statusHistory);

    } catch (error) {
      console.error('[SystemMonitor] 保存状态历史失败:', error);
    }
  }

  /**
   * 检查告警条件
   */
  checkAlerts(status) {
    try {
      if (!status || !status.checks) {
        return;
      }

      const alerts = [];

      // 安全地检查响应时间告警
      if (status.checks && typeof status.checks === 'object') {
        Object.entries(status.checks).forEach(([checkName, checkResult]) => {
          try {
            if (checkResult && checkResult.responseTime && 
                checkResult.responseTime > this.alertThresholds.responseTime) {
              alerts.push({
                type: 'performance',
                severity: 'warning',
                message: `${checkName}响应时间过长: ${checkResult.responseTime}ms`
              });
            }
          } catch (entryError) {
            // 忽略单个检查项的错误
          }
        });
      }

      // 检查错误率告警
      if (status.overallHealth === 'error') {
        alerts.push({
          type: 'availability',
          severity: 'error',
          message: '系统出现严重错误'
        });
      }

      // 如果有告警，记录但不通知用户（避免干扰）
      if (alerts.length > 0) {
        // 暂时禁用告警处理，避免影响页面正常使用
        // this.handleAlerts(alerts);
      }

    } catch (error) {
      console.error('[SystemMonitor] 告警检查失败:', error);
    }
  }

  /**
   * 处理告警
   */
  handleAlerts(alerts) {
    try {
      // 记录告警到本地
      const alertLog = {
        timestamp: new Date().toISOString(),
        alerts: alerts
      };

      let alertHistory = wx.getStorageSync('system_alerts') || [];
      alertHistory.push(alertLog);

      // 只保留最近20条告警记录
      if (alertHistory.length > 20) {
        alertHistory = alertHistory.slice(-20);
      }

      wx.setStorageSync('system_alerts', alertHistory);

      // 对于严重错误，可以考虑通知用户（但要避免过度打扰）
      const criticalAlerts = alerts.filter(alert => alert.severity === 'error');
      if (criticalAlerts.length > 0) {
        // 这里可以实现用户通知逻辑
        console.error('[SystemMonitor] 严重告警:', criticalAlerts);
      }

    } catch (error) {
      console.error('[SystemMonitor] 告警处理失败:', error);
    }
  }

  /**
   * 设置应用生命周期监控
   */
  setupAppLifecycleMonitoring() {
    wx.onAppHide(() => {
      this.monitoringEnabled = false;
    });

    wx.onAppShow(() => {
      this.monitoringEnabled = true;
      
      // 立即执行一次系统检查
      setTimeout(() => {
        this.performSystemCheck();
      }, 1000);
    });
  }

  /**
   * 设置网络状态监控
   */
  setupNetworkMonitoring() {
    wx.onNetworkStatusChange((res) => {
      // 网络状态变化时立即执行检查
      if (res.isConnected) {
        setTimeout(() => {
          this.performSystemCheck();
        }, 2000);
      }
    });
  }

  /**
   * 获取系统状态报告
   */
  getStatusReport() {
    try {
      const latestStatus = this.statusHistory[this.statusHistory.length - 1];
      const alertHistory = wx.getStorageSync('system_alerts') || [];
      
      return {
        currentStatus: latestStatus,
        statusHistory: this.statusHistory,
        alertHistory: alertHistory,
        monitoringEnabled: this.monitoringEnabled,
        thresholds: this.alertThresholds
      };

    } catch (error) {
      console.error('[SystemMonitor] 获取状态报告失败:', error);
      return null;
    }
  }

  /**
   * 手动触发系统检查
   */
  async triggerManualCheck() {
    return await this.performSystemCheck();
  }
}

// 创建全局实例
const systemMonitor = new SystemMonitor();

module.exports = {
  systemMonitor,
  SystemMonitor
};