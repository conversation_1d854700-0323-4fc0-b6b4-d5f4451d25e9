/**
 * 紧急修复工具
 * 专门解决 env check invalid 问题
 */

class EmergencyFix {
  constructor() {
    this.fixAttempts = 0
    this.maxAttempts = 10
    this.isFixing = false
  }

  /**
   * 紧急修复云开发环境问题
   */
  async emergencyFix() {
    if (this.isFixing) {
      console.log('🔄 修复正在进行中...')
      return { success: false, message: '修复正在进行中' }
    }

    this.isFixing = true
    this.fixAttempts++

    console.log(`🚨 开始第${this.fixAttempts}次紧急修复...`)

    try {
      // 步骤1：完全重置云开发状态
      await this.resetCloudState()

      // 步骤2：强制重新初始化
      await this.forceReinit()

      // 步骤3：验证修复结果
      const testResult = await this.verifyFix()

      if (testResult.success) {
        console.log('✅ 紧急修复成功！')
        this.isFixing = false
        return { success: true, message: '修复成功' }
      } else {
        throw new Error('修复验证失败: ' + testResult.error)
      }

    } catch (error) {
      console.error(`❌ 第${this.fixAttempts}次修复失败:`, error)
      this.isFixing = false

      if (this.fixAttempts < this.maxAttempts) {
        console.log(`⏳ 2秒后尝试下一次修复...`)
        setTimeout(() => this.emergencyFix(), 2000)
        return { success: false, message: '修复失败，正在重试' }
      } else {
        return { 
          success: false, 
          message: '多次修复失败，请手动检查环境配置',
          suggestions: this.getManualFixSuggestions()
        }
      }
    }
  }

  /**
   * 完全重置云开发状态
   */
  async resetCloudState() {
    console.log('🔄 重置云开发状态...')

    try {
      // 清除wx.cloud的所有内部状态
      if (typeof wx !== 'undefined' && wx.cloud) {
        // 保存原始方法
        const originalInit = wx.cloud.init
        const originalDatabase = wx.cloud.database

        // 清除所有内部属性
        Object.keys(wx.cloud).forEach(key => {
          if (key.startsWith('_') || key === 'database' || key === 'init') {
            delete wx.cloud[key]
          }
        })

        // 恢复核心方法
        wx.cloud.init = originalInit
        wx.cloud.database = originalDatabase

        console.log('✅ 云开发状态已重置')
      }

      // 清除应用全局状态
      const app = getApp()
      if (app && app.globalData) {
        app.globalData.db = null
        app.globalData.degradedMode = false
        console.log('✅ 应用状态已重置')
      }

    } catch (error) {
      console.warn('⚠️ 重置状态时出现警告:', error)
    }
  }

  /**
   * 强制重新初始化
   */
  async forceReinit() {
    console.log('🔄 强制重新初始化...')

    // 尝试多种初始化策略
    const strategies = [
      // 策略1：标准初始化
      () => this.standardInit(),
      // 策略2：最小配置初始化
      () => this.minimalInit(),
      // 策略3：延迟初始化
      () => this.delayedInit(),
      // 策略4：分步初始化
      () => this.stepByStepInit()
    ]

    for (let i = 0; i < strategies.length; i++) {
      try {
        console.log(`🔄 尝试策略${i + 1}...`)
        await strategies[i]()
        console.log(`✅ 策略${i + 1}成功！`)
        return
      } catch (error) {
        console.error(`❌ 策略${i + 1}失败:`, error)
        if (i < strategies.length - 1) {
          await this.sleep(1000)
        }
      }
    }

    throw new Error('所有初始化策略均失败')
  }

  /**
   * 标准初始化
   */
  async standardInit() {
    wx.cloud.init({
      env: 'cloud1-4g85f8xlb8166ff1',
      traceUser: true,
      timeout: 60000
    })

    await this.sleep(2000)
    
    const db = wx.cloud.database()
    await db.collection('users').limit(1).get()
  }

  /**
   * 最小配置初始化
   */
  async minimalInit() {
    wx.cloud.init({
      env: 'cloud1-4g85f8xlb8166ff1'
    })

    await this.sleep(1000)
    
    const db = wx.cloud.database()
    await db.collection('users').limit(1).get()
  }

  /**
   * 延迟初始化
   */
  async delayedInit() {
    // 先等待5秒
    await this.sleep(5000)

    wx.cloud.init({
      env: 'cloud1-4g85f8xlb8166ff1',
      traceUser: false,
      timeout: 30000
    })

    await this.sleep(3000)
    
    const db = wx.cloud.database()
    await db.collection('users').limit(1).get()
  }

  /**
   * 分步初始化
   */
  async stepByStepInit() {
    // 第一步：只初始化，不测试
    wx.cloud.init({
      env: 'cloud1-4g85f8xlb8166ff1'
    })
    await this.sleep(2000)

    // 第二步：获取数据库实例
    const db = wx.cloud.database()
    await this.sleep(1000)

    // 第三步：测试连接
    await db.collection('users').limit(1).get()
  }

  /**
   * 验证修复结果
   */
  async verifyFix() {
    try {
      console.log('🔍 验证修复结果...')

      const db = wx.cloud.database()
      await db.collection('users').limit(1).get()
      console.log('✅ 基本连接测试通过')

      try {
        await db.collection('test_fix').add({
          data: {
            timestamp: new Date(),
            test: 'emergency_fix'
          }
        })
        console.log('✅ 写入操作测试通过')
      } catch (writeError) {
        console.warn('⚠️ 写入操作测试失败，但读取正常:', writeError)
      }

      const app = getApp()
      if (app && app.globalData) {
        app.globalData.db = db
        app.globalData.degradedMode = false
        console.log('✅ 全局状态更新成功')
      }

      return { success: true }

    } catch (error) {
      console.error('❌ 验证失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取手动修复建议
   */
  getManualFixSuggestions() {
    return [
      '1. 检查微信开发者工具中的云开发环境是否正常',
      '2. 尝试重启微信开发者工具',
      '3. 检查网络连接是否稳定',
      '4. 确认云开发环境ID是否正确',
      '5. 尝试重新创建云开发环境',
      '6. 检查小程序基础库版本是否支持云开发',
      '7. 联系技术支持获取帮助'
    ]
  }

  /**
   * 延时函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 获取修复状态
   */
  getStatus() {
    return {
      isFixing: this.isFixing,
      fixAttempts: this.fixAttempts,
      maxAttempts: this.maxAttempts,
      timestamp: new Date().toISOString()
    }
  }
}

// 创建全局实例
const emergencyFix = new EmergencyFix()

// 导出
module.exports = {
  emergencyFix,
  EmergencyFix
}

// 如果在小程序环境中，提供全局访问
if (typeof getApp !== 'undefined') {
  try {
    const app = getApp()
    if (app && app.globalData) {
      app.globalData.emergencyFix = emergencyFix
    }
  } catch (error) {
    console.warn('无法设置全局紧急修复工具:', error)
  }
}
