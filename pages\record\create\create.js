/**
 * 快速记录页面 - 极简版
 * 3步完成记录：输入学生 → 描述行为 → 保存
 */
const app = getApp();
const { extractSurname } = require('../../../utils/globalUtils');

// 获取云服务实例的辅助函数
function getCloudService() {
  const cloudService = app.globalData.cloudService;
  if (!cloudService) {
    throw new Error('云服务未初始化，请稍后重试');
  }
  return cloudService;
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      behaviorType: '',
      action: '',
      customAction: '',
      score: 8,
      description: '',
      recordContent: ''
    },

    // 选中的学生
    selectedStudent: null,

    // 提交状态
    submitting: false,

    // 图片列表
    images: [],

    // 行为类型
    behaviorTypes: [
      { value: 'positive', name: '积极表现', emoji: '🌟' },
      { value: 'learning', name: '学习态度', emoji: '📚' },
      { value: 'social', name: '社交能力', emoji: '🤝' },
      { value: 'creative', name: '创新思维', emoji: '💡' },
      { value: 'discipline', name: '纪律表现', emoji: '⚖️' },
      { value: 'other', name: '其他表现', emoji: '✨' }
    ],

    // 具体行为（根据类型动态变化）
    quickActions: [],

    // 所有行为选项
    actionOptions: {
      positive: [
        { value: 'active_speak', name: '积极发言' },
        { value: 'help_others', name: '主动帮助同学' },
        { value: 'participate', name: '积极参与活动' },
        { value: 'leadership', name: '展现领导力' }
      ],
      learning: [
        { value: 'focus_class', name: '上课专注' },
        { value: 'homework_excellent', name: '作业优秀' },
        { value: 'ask_questions', name: '主动提问' },
        { value: 'study_hard', name: '学习努力' }
      ],
      social: [
        { value: 'team_work', name: '团队合作' },
        { value: 'polite', name: '礼貌待人' },
        { value: 'share', name: '乐于分享' },
        { value: 'friendly', name: '友善相处' }
      ],
      creative: [
        { value: 'innovative', name: '创新思维' },
        { value: 'creative_work', name: '创意作品' },
        { value: 'unique_idea', name: '独特想法' },
        { value: 'problem_solve', name: '解决问题' }
      ],
      discipline: [
        { value: 'follow_rules', name: '遵守纪律' },
        { value: 'punctual', name: '准时到校' },
        { value: 'organized', name: '整理有序' },
        { value: 'responsible', name: '负责任' }
      ],
      other: [
        { value: 'progress', name: '明显进步' },
        { value: 'effort', name: '努力表现' },
        { value: 'special', name: '特殊表现' },
        { value: 'custom', name: '自定义' }
      ]
    },

    // 图片列表
    images: [],

    // 学生相关数据
    allStudents: [],
    filteredStudents: [],
    showStudentPopup: false,
    studentSearchKeyword: '',

    // 提交状态
    submitting: false,

    // 显示模板选择
    showTemplates: false,

    // 保存成功弹窗
    showSaveSuccessDialog: false,
    savedRecordData: null,

    // 魔法提示
    magicTips: [
      '具体描述比泛泛而谈更有价值',
      '记录真实瞬间，让评语更生动',
      '每个孩子都有闪光点，用心发现'
    ],
    currentTip: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置随机提示
    this.setRandomTip();

    // 订阅学生数据变更事件
    this.subscribeToDataChanges();

    // 加载学生数据
    this.loadStudentData();

    // 确保弹窗初始状态为关闭
    this.setData({
      showStudentPopup: false
    });

    // 如果有传入的学生信息，预填充
    if (options.studentName) {
      this.setData({
        'formData.studentName': decodeURIComponent(options.studentName)
      });
    }
  },

  /**
   * 加载学生数据（使用统一数据管理器）
   */
  async loadStudentData() {
    try {

      // 使用统一数据管理器获取数据
      const { getStudents } = require('../../../utils/unifiedDataManager');
      const result = await getStudents({
        pageId: 'record-create',
        forceRefresh: false
      });

      if (result.success) {
        const students = result.data.map(student => ({
          id: student._id,
          name: student.name,
          surname: extractSurname(student.name),
          className: student.className,
          studentNumber: student.studentId,
          classId: student.classId
        }));

        this.setData({
          allStudents: students,
          filteredStudents: students
        });

      } else {

        this.setData({
          allStudents: [],
          filteredStudents: []
        });
      }
    } catch (error) {
      console.error('❌ 快速记录页面加载学生数据异常:', error);
      this.setData({
        allStudents: [],
        filteredStudents: []
      });
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新学生数据
    this.loadStudentData();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

    // 取消数据变更事件订阅
    if (this.unsubscribeStudents) {
      this.unsubscribeStudents();
    }
  },

  /**
   * 订阅数据变更事件
   */
  subscribeToDataChanges() {
    const { subscribe } = require('../../../utils/dataEventBus');

    // 订阅学生数据变更
    this.unsubscribeStudents = subscribe('students', (data, operation, source) => {

      // 更新页面数据
      const students = data.map(student => ({
        id: student._id,
        name: student.name,
        surname: extractSurname(student.name),
        className: student.className,
        studentNumber: student.studentId,
        classId: student.classId
      }));

      this.setData({
        allStudents: students,
        filteredStudents: students
      });

      // 如果数据被清空，显示提示
      if (operation === 'clear' && data.length === 0) {
        wx.showToast({
          title: '学生数据已清空',
          icon: 'none',
          duration: 2000
        });
      }
    }, 'record-create');
  },

  /**
   * 设置随机魔法提示
   */
  setRandomTip() {
    const { magicTips } = this.data;
    const randomIndex = Math.floor(Math.random() * magicTips.length);
    this.setData({
      currentTip: magicTips[randomIndex]
    });
  },

  /**
   * 选择行为类型
   */
  selectBehaviorType(e) {
    const { type } = e.currentTarget.dataset;
    const { actionOptions } = this.data;

    this.setData({
      'formData.behaviorType': type,
      'formData.action': '',
      'formData.customAction': '',
      quickActions: actionOptions[type] || []
    });
  },

  /**
   * 选择具体行为
   */
  selectAction(e) {
    const { action } = e.currentTarget.dataset;
    this.setData({
      'formData.action': action,
      'formData.customAction': ''
    });
  },

  /**
   * 自定义行为输入
   */
  onCustomActionChange(e) {
    this.setData({
      'formData.customAction': e.detail.value,
      'formData.action': ''
    });
  },

  /**
   * 评分变化
   */
  onScoreChange(e) {

    const score = e.detail.value;
    this.setData({
      'formData.score': score
    });
  },

  /**
   * 描述输入
   */
  onDescriptionChange(e) {
    this.setData({
      'formData.description': e.detail.value
    });
  },

  /**
   * 记录内容输入
   */
  onRecordContentChange(e) {
    this.setData({
      'formData.recordContent': e.detail.value
    });
  },

  /**
   * 显示学生选择器
   */
  showStudentPicker() {

    // 为学生数据添加选中状态
    const studentsWithSelection = this.data.allStudents.map(student => ({
      ...student,
      isSelected: this.data.selectedStudent && this.data.selectedStudent.id === student.id
    }));

    this.setData({
      showStudentPopup: true,
      studentSearchKeyword: '',
      filteredStudents: studentsWithSelection
    });

  },

  /**
   * 隐藏学生选择器
   */
  hideStudentPicker() {
    this.setData({
      showStudentPopup: false,
      studentSearchKeyword: ''
    });
  },

  /**
   * 学生搜索
   */
  onStudentSearchChange(e) {
    const keyword = e.detail.value.trim();
    const { allStudents } = this.data;

    let filteredStudents = allStudents;
    if (keyword) {
      filteredStudents = allStudents.filter(student =>
        student.name.includes(keyword) || student.className.includes(keyword)
      );
    }

    // 为筛选后的学生添加选中状态
    const filteredWithSelection = filteredStudents.map(student => ({
      ...student,
      isSelected: this.data.selectedStudent && this.data.selectedStudent.id === student.id
    }));

    this.setData({
      studentSearchKeyword: keyword,
      filteredStudents: filteredWithSelection
    });
  },

  /**
   * 清除搜索
   */
  onStudentSearchClear() {
    // 为学生数据添加选中状态
    const studentsWithSelection = this.data.allStudents.map(student => ({
      ...student,
      isSelected: this.data.selectedStudent && this.data.selectedStudent.id === student.id
    }));

    this.setData({
      studentSearchKeyword: '',
      filteredStudents: studentsWithSelection
    });
  },

  /**
   * 选择学生
   */
  selectStudent(e) {
    const { student } = e.currentTarget.dataset;

    // 更新filteredStudents中的选中状态
    const updatedFilteredStudents = this.data.filteredStudents.map(s => ({
      ...s,
      isSelected: s.id === student.id
    }));

    this.setData({
      selectedStudent: student,
      filteredStudents: updatedFilteredStudents,
      showStudentPopup: false,
      studentSearchKeyword: ''
    });

  },

  /**
   * 阻止弹窗关闭
   */
  preventClose() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭
  },

  /**
   * 选择图片
   */
  chooseImage() {
    const { images } = this.data;
    const remainCount = 3 - images.length;

    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newImages = [...images, ...res.tempFilePaths];
        this.setData({ images: newImages });
      }
    });
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const { index } = e.currentTarget.dataset;
    const { images } = this.data;

    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  /**
   * 删除图片
   */
  deleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const { images } = this.data;

    images.splice(index, 1);
    this.setData({ images });
  },

  /**
   * 学生姓名输入
   */
  onStudentNameChange(e) {
    this.setData({
      'formData.studentName': e.detail.value
    });
  },

  /**
   * 行为描述输入
   */
  onBehaviorChange(e) {
    this.setData({
      'formData.behavior': e.detail.value
    });
  },

  /**
   * 选择心情
   */
  onMoodChange(e) {
    this.setData({
      'formData.mood': e.detail.value
    });
  },

  /**
   * 显示行为模板
   */
  showBehaviorTemplates() {
    this.setData({
      showTemplates: !this.data.showTemplates
    });
  },

  /**
   * 选择行为模板
   */
  selectTemplate(e) {
    const template = e.currentTarget.dataset.template;
    this.setData({
      'formData.behavior': template,
      showTemplates: false
    });
  },

  /**
   * 快速添加到行为描述
   */
  appendToBehavior(text) {
    const currentBehavior = this.data.formData.behavior;
    const newBehavior = currentBehavior ? `${currentBehavior}，${text}` : text;
    this.setData({
      'formData.behavior': newBehavior
    });
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.studentName.trim()) {
      wx.showToast({
        title: '请输入学生姓名',
        icon: 'none'
      });
      return false;
    }

    if (!formData.behavior.trim()) {
      wx.showToast({
        title: '请描述学生行为',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  /**
   * 保存记录
   */
  async saveRecord() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    try {
      const recordData = {
        studentName: this.data.formData.studentName.trim(),
        behavior: this.data.formData.behavior.trim(),
        mood: this.data.formData.mood,
        timestamp: new Date(),
        source: 'quick_create_page'
      };

      // 调用云服务保存记录
      const cloudService = getCloudService();
      const result = await cloudService.saveQuickRecord(recordData);

      if (result.success) {
        // 保存成功提示
        wx.showToast({
          title: '记录成功！',
          icon: 'success',
          duration: 2000
        });

        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);

        // 清空表单以便继续记录
        this.resetForm();
      } else {
        throw new Error(result.error || '保存失败');
      }
    } catch (error) {
      console.error('保存记录失败:', error);
      wx.showToast({
        title: error.message || '保存失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      formData: {
        studentName: '',
        behavior: '',
        mood: 'positive'
      },
      showTemplates: false
    });
    this.setRandomTip();
  },

  /**
   * 继续记录（保存后继续添加）
   */
  async saveAndContinue() {
    await this.saveRecord();
    // saveRecord内部已经调用了resetForm
  },

  /**
   * 预览AI生成效果
   */
  previewAIGeneration() {
    if (!this.validateForm()) {
      return;
    }

    const { formData } = this.data;
    
    // 构造跳转参数
    const query = `studentName=${encodeURIComponent(formData.studentName)}&behavior=${encodeURIComponent(formData.behavior)}&mood=${formData.mood}`;
    
    wx.navigateTo({
      url: `/pages/comment/generate/generate?${query}`
    });
  },

  /**
   * 提交记录
   */
  async onSubmit(e) {

    const { selectedStudent, formData, images, submitting } = this.data;

    // 防止重复提交
    if (submitting) {

      return;
    }

    // 验证必填项
    if (!selectedStudent) {
      wx.showToast({
        title: '请先选择学生',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (!formData.recordContent.trim()) {
      wx.showToast({
        title: '请输入记录内容',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 设置提交状态
    this.setData({ submitting: true });

    try {
      // 真实保存到云数据库

      // 构造记录数据
      const recordData = {
        studentId: selectedStudent.id,
        studentName: selectedStudent.name,
        className: selectedStudent.className,
        content: formData.recordContent.trim(),
        score: formData.score,
        description: formData.description.trim() || '',
        images: images,
        behaviorType: formData.behaviorType || 'general',
        createTime: new Date(),
        teacherId: app.globalData.userInfo?.id || 'temp_teacher'
      };

      // 使用云服务保存记录
      const cloudService = getCloudService();
      const saveResult = await cloudService.addRecord(recordData);

      if (!saveResult.success) {
        throw new Error(saveResult.error || '保存到云数据库失败');
      }

      // 保存成功，显示操作选择弹窗
      this.showSaveSuccessDialog(saveResult.data);

    } catch (error) {
      console.error('保存记录失败:', error);
      wx.showToast({
        title: error.message || '保存失败，请重试',
        icon: 'none',
        duration: 3000
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 显示保存成功弹窗
   */
  showSaveSuccessDialog(recordData) {

    this.setData({
      showSaveSuccessDialog: true,
      savedRecordData: {
        ...recordData,
        studentId: this.data.selectedStudent.id,
        studentName: this.data.selectedStudent.name
      }
    });
  },

  /**
   * 隐藏保存成功弹窗
   */
  hideSaveSuccessDialog() {
    this.setData({
      showSaveSuccessDialog: false,
      savedRecordData: null
    });
  },

  /**
   * 阻止弹窗内容区域的点击事件冒泡
   */
  preventClose() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
  },

  /**
   * 阻止弹窗内容区域的点击事件冒泡
   */
  preventClose() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
  },

  /**
   * 重新编辑记录
   */
  onReEditRecord() {

    this.hideSaveSuccessDialog();
    // 保持当前页面状态，用户可以继续编辑
  },

  /**
   * 查看学生记录详情
   */
  onViewStudentDetail() {

    const { savedRecordData } = this.data;

    if (savedRecordData && savedRecordData.studentId) {
      this.hideSaveSuccessDialog();

      // 跳转到学生详情页面
      wx.navigateTo({
        url: `/pages/student/detail/detail?studentId=${savedRecordData.studentId}`,
        success: () => {

        },
        fail: (error) => {
          console.error('跳转学生详情页面失败:', error);
          wx.showToast({
            title: '跳转失败，请重试',
            icon: 'none'
          });
        }
      });
    } else {
      wx.showToast({
        title: '学生信息异常',
        icon: 'none'
      });
    }
  },

  /**
   * 继续记录（清空表单，保持学生选择）
   */
  onContinueRecord() {

    this.hideSaveSuccessDialog();

    // 清空表单内容，但保持学生选择
    this.setData({
      formData: {
        behaviorType: '',
        action: '',
        customAction: '',
        score: 8,
        description: '',
        recordContent: ''
      },
      images: []
    });

    // 显示成功提示
    wx.showToast({
      title: '可以继续记录了',
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 记录其他学生
   */
  onRecordOtherStudent() {

    this.hideSaveSuccessDialog();

    // 重置表单和学生选择
    this.setData({
      selectedStudent: null,
      formData: {
        behaviorType: '',
        action: '',
        customAction: '',
        score: 8,
        description: '',
        recordContent: ''
      },
      images: []
    });

    // 显示学生选择器
    this.showStudentPicker();

    // 显示提示
    wx.showToast({
      title: '请选择要记录的学生',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: 'AI评语助手 - 快速记录学生行为',
      path: '/pages/record/create/create',
      imageUrl: '/assets/images/share-record.png'
    };
  }
});