/**
 * 学生详情页面
 * 显示学生基本信息和行为记录
 */
const app = getApp();
const { cloudService } = require('../../../services/cloudService');
const { extractSurname } = require('../../../utils/globalUtils');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 学生基本信息
    studentInfo: {},
    
    // 记录列表
    recordList: [],
    
    // 统计信息
    statistics: {
      totalRecords: 0,
      positiveCount: 0,
      negativeCount: 0,
      thisWeekCount: 0
    },
    
    // 筛选选项
    filterType: '', // all, positive, negative, academic, social, creative
    filterTypes: [
      { value: '', label: '全部记录' },
      { value: 'positive', label: '积极表现' },
      { value: 'academic', label: '学习相关' },
      { value: 'social', label: '社交能力' },
      { value: 'creative', label: '创新表现' },
      { value: 'negative', label: '需要改进' }
    ],
    
    // 加载状态
    loading: false,
    
    // 操作菜单
    showActionSheet: false,
    actionOptions: [
      { name: '添加记录', value: 'addRecord' },
      { name: '生成评语', value: 'generateComment' },
      { name: '编辑信息', value: 'editStudent' },
      { name: '导出记录', value: 'exportRecords' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { studentId } = options;
    if (studentId) {
      this.setData({ studentId });
      this.loadStudentDetail(studentId);
    } else {
      wx.showToast({
        title: '学生ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新数据
    if (this.data.studentId) {
      this.loadRecordList();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadStudentDetail(this.data.studentId).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 加载学生详情
   */
  async loadStudentDetail(studentId) {
    try {
      this.setData({ loading: true });

      // 获取学生基本信息
      const studentResult = await cloudService.getStudentDetail(studentId);
      if (!studentResult.success) {
        throw new Error('获取学生信息失败');
      }

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: (studentResult.data.name || '学生') + ' - 详情'
      });

      // 添加姓氏信息到学生数据，并映射学号字段
      const studentInfo = {
        ...studentResult.data,
        surname: extractSurname(studentResult.data.name),
        studentNumber: studentResult.data.studentId // 映射学号字段
      };
      
      this.setData({
        studentInfo: studentInfo,
        loading: false
      });

      // 加载记录列表
      await this.loadRecordList();

    } catch (error) {
      console.error('加载学生详情失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载记录列表
   */
  async loadRecordList() {
    try {
      const { studentId, filterType } = this.data;

      const params = {
        studentId: studentId,
        pageSize: 100 // 获取更多记录
      };

      if (filterType) {
        params.behaviorType = filterType;
      }

      const result = await cloudService.getRecordList(params);

      if (result.success) {
        const records = result.data || [];

        // 处理记录数据，格式化时间
        const processedRecords = records.map(record => ({
          ...record,
          createTime: this.formatTime(record.createTime),
          id: record._id // 确保有ID用于key
        }));
        
        // 计算统计信息
        const statistics = this.calculateStatistics(records);

        this.setData({
          recordList: processedRecords,
          statistics: statistics
        });

      } else {

        this.setData({
          recordList: [],
          statistics: {
            totalRecords: 0,
            positiveCount: 0,
            negativeCount: 0,
            thisWeekCount: 0
          }
        });
      }

    } catch (error) {
      console.error('加载记录列表失败:', error);
      wx.showToast({
        title: '加载记录失败',
        icon: 'none'
      });
    }
  },

  /**
   * 计算统计信息
   */
  calculateStatistics(records) {
    const total = records.length;
    const positive = records.filter(r => r.behaviorType === 'positive' || r.behaviorType === 'academic' || r.behaviorType === 'social' || r.behaviorType === 'creative').length;
    const negative = records.filter(r => r.behaviorType === 'negative').length;
    
    // 计算本周记录数
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    const thisWeek = records.filter(r => new Date(r.createTime) > oneWeekAgo).length;

    return {
      totalRecords: total,
      positiveCount: positive,
      negativeCount: negative,
      thisWeekCount: thisWeek
    };
  },

  /**
   * 筛选记录
   */
  onFilterChange(e) {
    const filterType = e.detail.value;
    const selectedFilter = this.data.filterTypes[filterType];
    
    this.setData({
      filterType: selectedFilter.value
    });
    
    this.loadRecordList();
  },

  /**
   * 点击记录项 - 显示完整记录详情包括图片
   */
  onRecordTap(e) {
    const { record } = e.currentTarget.dataset;

    // 如果记录有图片，跳转到记录详情页面查看完整信息
    if (record.images && record.images.length > 0) {
      wx.navigateTo({
        url: `/pages/record/detail/detail?id=${record._id || record.id}`,
        fail: (error) => {
          console.error('跳转记录详情页失败:', error);
          // 跳转失败时显示弹窗
          this.showRecordModal(record);
        }
      });
    } else {
      // 没有图片时显示弹窗
      this.showRecordModal(record);
    }
  },

  /**
   * 获取行为类型文本
   */
  getBehaviorTypeText(type) {
    const typeMap = {
      'positive': '积极行为',
      'negative': '消极行为',
      'academic': '学习表现',
      'social': '社交行为',
      'creative': '创新表现',
      'discipline': '纪律表现'
    };
    return typeMap[type] || '其他行为';
  },

  /**
   * 显示记录详情弹窗
   */
  showRecordModal(record) {
    const behaviorTypeText = this.getBehaviorTypeText(record.behaviorType);
    const content = `行为类型：${behaviorTypeText}\n具体内容：${record.content || record.action || '无描述'}\n记录时间：${record.createTime}\n评分：${record.score || '无评分'}${record.description ? '\n详细描述：' + record.description : ''}`;

    wx.showModal({
      title: '📋 行为记录详情',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 显示操作菜单
   */
  showActions() {
    this.setData({ showActionSheet: true });
  },

  /**
   * 操作菜单选择
   */
  onActionSelect(e) {
    const { value } = e.detail;
    this.setData({ showActionSheet: false });

    switch (value) {
      case 'addRecord':
        this.navigateToAddRecord();
        break;
      case 'generateComment':
        this.navigateToGenerateComment();
        break;
      case 'editStudent':
        this.navigateToEditStudent();
        break;
      case 'exportRecords':
        this.exportRecords();
        break;
    }
  },

  /**
   * 跳转到添加记录
   */
  navigateToAddRecord() {
    const { studentInfo } = this.data;

    if (!studentInfo || !studentInfo._id) {
      wx.showToast({
        title: '学生信息加载中，请稍后',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/record/create/create?studentId=${studentInfo._id}&studentName=${encodeURIComponent(studentInfo.name)}`
    });
  },

  /**
   * 跳转到生成评语
   */
  navigateToGenerateComment() {
    const { studentInfo } = this.data;
    wx.navigateTo({
      url: `/pages/comment/generate/generate?studentId=${studentInfo._id}&studentName=${studentInfo.name}`
    });
  },

  /**
   * 跳转到编辑学生
   */
  navigateToEditStudent() {
    const { studentInfo } = this.data;
    wx.navigateTo({
      url: `/pages/student/create/create?mode=edit&studentId=${studentInfo._id}`
    });
  },

  /**
   * 导出记录
   */
  async exportRecords() {
    try {
      const { recordList, studentInfo } = this.data;
      
      if (recordList.length === 0) {
        wx.showToast({
          title: '暂无记录可导出',
          icon: 'none'
        });
        return;
      }

      wx.showLoading({ title: '导出中...' });

      // 生成导出内容
      const content = this.generateExportContent(recordList, studentInfo);
      
      // 保存文件
      const fileName = `${studentInfo.name}_行为记录_${new Date().toISOString().slice(0, 10)}.txt`;
      
      wx.hideLoading();
      wx.showModal({
        title: '导出成功',
        content: `记录已导出为：${fileName}`,
        showCancel: false
      });

    } catch (error) {
      wx.hideLoading();
      console.error('导出记录失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生成导出内容
   */
  generateExportContent(records, studentInfo) {
    let content = `学生：${studentInfo.name}\n`;
    content += `班级：${studentInfo.className}\n`;
    content += `学号：${studentInfo.studentNumber || '无'}\n`;
    content += `导出时间：${new Date().toLocaleString()}\n`;
    content += `记录总数：${records.length}\n\n`;
    
    content += '=== 行为记录明细 ===\n\n';
    
    records.forEach((record, index) => {
      content += `${index + 1}. ${record.action}\n`;
      content += `   时间：${this.formatTime(record.createTime)}\n`;
      content += `   类型：${this.getTypeName(record.behaviorType)}\n`;
      if (record.description) {
        content += `   描述：${record.description}\n`;
      }
      content += '\n';
    });

    return content;
  },

  /**
   * 获取类型名称
   */
  getTypeName(type) {
    const typeMap = {
      positive: '积极表现',
      negative: '需要改进',
      academic: '学习相关',
      social: '社交能力',
      creative: '创新表现'
    };
    return typeMap[type] || '其他';
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '未知时间';
    
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return time.toLocaleDateString();
    }
  },

  /**
   * 关闭操作菜单
   */
  hideActionSheet() {
    this.setData({ showActionSheet: false });
  }
});