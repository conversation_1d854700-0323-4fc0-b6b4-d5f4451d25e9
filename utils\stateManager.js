/**
 * 全局状态管理器
 * 提供应用级别的状态管理和数据同步
 */

class StateManager {
  constructor() {
    this.state = {
      // 用户信息
      userInfo: null,
      
      // 数据缓存
      classList: [],
      studentList: [],
      recentComments: [],
      
      // 统计数据
      statistics: {
        totalClasses: 0,
        totalStudents: 0,
        totalRecords: 0,
        totalComments: 0
      },
      
      // 应用状态
      isLoading: false,
      networkStatus: 'online',
      lastSyncTime: null,
      
      // AI配置
      aiConfig: {
        provider: 'doubao',
        apiKey: '',
        model: 'doubao-lite-4k',
        temperature: 0.7
      }
    };
    
    this.listeners = new Map();
    this.init();
  }

  /**
   * 初始化状态管理器
   */
  init() {
    // 从本地存储恢复状态
    this.loadFromStorage();
    
    // 监听网络状态
    this.setupNetworkMonitor();
    
    // 设置定时同步
    this.setupAutoSync();
  }

  /**
   * 从本地存储加载状态
   */
  loadFromStorage() {
    try {
      const savedState = wx.getStorageSync('appState');
      if (savedState) {
        this.state = { ...this.state, ...savedState };
      }
      
      // 加载AI配置
      const aiConfig = wx.getStorageSync('aiConfig');
      if (aiConfig) {
        this.state.aiConfig = { ...this.state.aiConfig, ...aiConfig };
      }
    } catch (error) {
      console.error('加载本地状态失败:', error);
    }
  }

  /**
   * 保存状态到本地存储
   */
  saveToStorage() {
    try {
      const stateToSave = {
        userInfo: this.state.userInfo,
        classList: this.state.classList,
        studentList: this.state.studentList,
        statistics: this.state.statistics,
        lastSyncTime: this.state.lastSyncTime
      };

      wx.setStorageSync('appState', stateToSave);
      wx.setStorageSync('aiConfig', this.state.aiConfig);
    } catch (error) {
      console.error('保存状态失败:', error);
    }
  }

  /**
   * 清空所有状态数据
   */
  clearAllState() {
    try {
      // 重置内存状态
      this.state = {
        userInfo: null,
        classList: [],
        studentList: [],
        statistics: {
          totalStudents: 0,
          totalComments: 0,
          totalRecords: 0
        },
        aiConfig: {
          apiKey: '',
          model: 'doubao-pro-4k',
          temperature: 0.7
        },
        lastSyncTime: null
      };

      // 清空本地存储
      wx.removeStorageSync('appState');
      wx.removeStorageSync('studentList');
      wx.removeStorageSync('classList');
      wx.removeStorageSync('aiConfig');

      // 清空徽章系统相关数据
      wx.removeStorageSync('unlockedAchievements');
      wx.removeStorageSync('achievementProgress');
      wx.removeStorageSync('achievementCache');

      // 清空其他应用数据
      wx.removeStorageSync('recordList');
      wx.removeStorageSync('commentList');
      wx.removeStorageSync('savedComments');
      wx.removeStorageSync('recentComments');
      wx.removeStorageSync('generatedComments');
      wx.removeStorageSync('userStats');
      wx.removeStorageSync('aiUsageStats');

    } catch (error) {
      console.error('清空状态失败:', error);
    }
  }

  /**
   * 获取状态
   */
  getState(key) {
    if (key) {
      return this.state[key];
    }
    return this.state;
  }

  /**
   * 设置状态
   */
  setState(key, value) {
    if (typeof key === 'object') {
      // 批量更新
      Object.keys(key).forEach(k => {
        this.state[k] = key[k];
      });
    } else {
      this.state[key] = value;
    }
    
    // 通知监听器
    this.notifyListeners(key, value);
    
    // 保存到本地存储
    this.saveToStorage();
  }

  /**
   * 订阅状态变化
   */
  subscribe(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, []);
    }
    this.listeners.get(key).push(callback);
    
    // 返回取消订阅函数
    return () => {
      const callbacks = this.listeners.get(key);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  /**
   * 通知监听器
   */
  notifyListeners(key, value) {
    if (typeof key === 'object') {
      // 批量更新时通知所有相关监听器
      Object.keys(key).forEach(k => {
        const callbacks = this.listeners.get(k);
        if (callbacks) {
          callbacks.forEach(callback => callback(key[k], k));
        }
      });
    } else {
      const callbacks = this.listeners.get(key);
      if (callbacks) {
        callbacks.forEach(callback => callback(value, key));
      }
    }
  }

  /**
   * 设置网络监控
   */
  setupNetworkMonitor() {
    wx.onNetworkStatusChange((res) => {
      this.setState('networkStatus', res.isConnected ? 'online' : 'offline');
      
      if (res.isConnected) {
        // 网络恢复时自动同步
        this.syncData();
      }
    });
  }

  /**
   * 设置自动同步
   */
  setupAutoSync() {
    // 每5分钟自动同步一次
    setInterval(() => {
      if (this.state.networkStatus === 'online') {
        this.syncData();
      }
    }, 5 * 60 * 1000);
  }

  /**
   * 同步数据
   */
  async syncData() {
    if (this.state.isLoading) return;
    
    this.setState('isLoading', true);
    
    try {
      const app = getApp();
      const cloudService = app.globalData.cloudService;
      
      if (!cloudService) {
        throw new Error('云服务未初始化');
      }
      
      // 同步班级数据
      const classResult = await cloudService.getClassList();
      if (classResult.success) {
        this.setState('classList', classResult.data);
      }
      
      // 同步学生数据
      const studentResult = await cloudService.getStudentList();
      if (studentResult.success) {
        this.setState('studentList', studentResult.data);
      }
      
      // 同步统计数据
      const statsResult = await cloudService.getCommentStatistics();
      if (statsResult.success) {
        this.setState('statistics', statsResult.data);
      }
      
      this.setState('lastSyncTime', new Date().getTime());
      
    } catch (error) {
      console.error('数据同步失败:', error);
    } finally {
      this.setState('isLoading', false);
    }
  }

  /**
   * 清除所有数据
   */
  clearAll() {
    this.state = {
      userInfo: null,
      classList: [],
      studentList: [],
      recentComments: [],
      statistics: {
        totalClasses: 0,
        totalStudents: 0,
        totalRecords: 0,
        totalComments: 0
      },
      isLoading: false,
      networkStatus: 'online',
      lastSyncTime: null,
      aiConfig: {
        provider: 'doubao',
        apiKey: '',
        model: 'doubao-lite-4k',
        temperature: 0.7
      }
    };
    
    // 清除本地存储
    wx.removeStorageSync('appState');
    wx.removeStorageSync('aiConfig');
    
    // 通知所有监听器
    this.listeners.forEach((callbacks, key) => {
      callbacks.forEach(callback => callback(this.state[key], key));
    });
  }

  /**
   * 获取格式化的同步时间
   */
  getFormattedSyncTime() {
    if (!this.state.lastSyncTime) {
      return '从未同步';
    }
    
    const now = new Date();
    const syncTime = new Date(this.state.lastSyncTime);
    const diff = now - syncTime;
    
    if (diff < 60000) {
      return '刚刚';
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return `${Math.floor(diff / 86400000)}天前`;
    }
  }
}

// 创建全局实例
const stateManager = new StateManager();

module.exports = stateManager;
