<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云函数连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .test-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .result-area {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-loading { background-color: #17a2b8; animation: pulse 1s infinite; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <h1>🔧 云函数连接测试工具</h1>
    
    <div class="test-card">
        <h2>📡 连接状态</h2>
        <div id="connectionStatus">
            <span class="status-indicator status-loading"></span>
            <span>正在检测连接状态...</span>
        </div>
        <button class="test-button" onclick="testConnection()">重新检测</button>
    </div>

    <div class="test-card">
        <h2>🧪 功能测试</h2>
        <div style="margin-bottom: 15px;">
            <button class="test-button" onclick="testHealthCheck()">健康检查</button>
            <button class="test-button" onclick="testGetModels()">获取AI模型</button>
            <button class="test-button" onclick="testCreateModel()">创建模型</button>
            <button class="test-button" onclick="testAuth()">权限测试</button>
        </div>
        <div id="testResults" class="result-area"></div>
    </div>

    <div class="test-card">
        <h2>🔍 诊断信息</h2>
        <button class="test-button" onclick="showDiagnostics()">显示诊断信息</button>
        <div id="diagnostics" class="result-area"></div>
    </div>

    <script src="https://web.sdk.qcloud.com/tcb-js-sdk/1.19.2/tcb.js"></script>
    <script>
        let cloudbaseApp = null;
        let testResults = '';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            testResults += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            document.getElementById('testResults').innerHTML = testResults;
            document.getElementById('testResults').scrollTop = document.getElementById('testResults').scrollHeight;
        }

        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connectionStatus');
            const indicator = statusElement.querySelector('.status-indicator');
            const text = statusElement.querySelector('span:last-child');
            
            indicator.className = `status-indicator status-${status}`;
            text.textContent = message;
        }

        async function initCloudbase() {
            try {
                if (cloudbaseApp) return cloudbaseApp;

                cloudbaseApp = tcb.init({
                    env: 'cloud1-4g85f8xlb8166ff1',
                    region: 'ap-shanghai'
                });

                // 匿名登录
                const auth = cloudbaseApp.auth();
                await auth.signInAnonymously();
                
                log('✅ 云开发SDK初始化成功', 'success');
                return cloudbaseApp;
            } catch (error) {
                log('❌ 云开发SDK初始化失败: ' + error.message, 'error');
                throw error;
            }
        }

        async function testConnection() {
            updateConnectionStatus('loading', '正在检测连接状态...');
            
            try {
                await initCloudbase();
                updateConnectionStatus('success', '连接正常');
                log('🔗 云开发连接测试成功', 'success');
            } catch (error) {
                updateConnectionStatus('error', '连接失败: ' + error.message);
                log('❌ 云开发连接测试失败: ' + error.message, 'error');
            }
        }

        async function testHealthCheck() {
            log('🔄 开始健康检查...', 'info');
            
            try {
                const app = await initCloudbase();
                const result = await app.callFunction({
                    name: 'adminAPI',
                    data: { action: 'healthCheck' }
                });

                if (result.result && result.result.code === 200) {
                    log('✅ 健康检查通过', 'success');
                    log(JSON.stringify(result.result.data, null, 2), 'info');
                } else {
                    log('⚠️ 健康检查异常: ' + JSON.stringify(result.result), 'warning');
                }
            } catch (error) {
                log('❌ 健康检查失败: ' + error.message, 'error');
            }
        }

        async function testGetModels() {
            log('🔄 开始获取AI模型列表...', 'info');
            
            try {
                const app = await initCloudbase();
                const result = await app.callFunction({
                    name: 'adminAPI',
                    data: { action: 'ai.getModels' }
                });

                if (result.result && result.result.code === 200) {
                    log('✅ 获取AI模型成功: ' + result.result.data.length + ' 个', 'success');
                    log(JSON.stringify(result.result.data, null, 2), 'info');
                } else {
                    log('⚠️ 获取AI模型失败: ' + JSON.stringify(result.result), 'warning');
                }
            } catch (error) {
                log('❌ 获取AI模型异常: ' + error.message, 'error');
            }
        }

        async function testCreateModel() {
            log('🔄 开始测试创建AI模型...', 'info');
            
            try {
                const app = await initCloudbase();
                const result = await app.callFunction({
                    name: 'adminAPI',
                    data: {
                        action: 'ai.createModel',
                        name: '测试模型_' + Date.now(),
                        provider: 'openai',
                        model: 'gpt-3.5-turbo',
                        config: {
                            apiKey: 'sk-test123',
                            baseUrl: 'https://api.openai.com/v1'
                        },
                        inputPrice: 0.001,
                        outputPrice: 0.002
                    }
                });

                if (result.result && result.result.code === 200) {
                    log('✅ 创建AI模型成功', 'success');
                    log(JSON.stringify(result.result.data, null, 2), 'info');
                } else {
                    log('⚠️ 创建AI模型失败: ' + JSON.stringify(result.result), 'warning');
                }
            } catch (error) {
                log('❌ 创建AI模型异常: ' + error.message, 'error');
            }
        }

        async function testAuth() {
            log('🔄 开始权限测试...', 'info');
            
            try {
                const app = await initCloudbase();
                const auth = app.auth();
                const loginState = await auth.getLoginState();
                
                log('🔍 当前登录状态:', 'info');
                log('- 是否匿名: ' + (loginState?.isAnonymous || false), 'info');
                log('- 用户ID: ' + (loginState?.user?.uid || '无'), 'info');
                log('- OpenID: ' + (loginState?.user?.customUserId || '无'), 'info');
                
                // 测试需要权限的操作
                const result = await app.callFunction({
                    name: 'adminAPI',
                    data: { action: 'auth.getUserInfo' }
                });
                
                if (result.result) {
                    log('✅ 权限测试完成: ' + JSON.stringify(result.result), 'success');
                } else {
                    log('⚠️ 权限测试无响应', 'warning');
                }
            } catch (error) {
                log('❌ 权限测试失败: ' + error.message, 'error');
            }
        }

        async function showDiagnostics() {
            const diagnosticsDiv = document.getElementById('diagnostics');
            
            const info = {
                '浏览器': navigator.userAgent,
                '当前时间': new Date().toISOString(),
                '页面URL': window.location.href,
                '本地存储': {
                    'aiModels': localStorage.getItem('aiModels') ? 'exists' : 'not found',
                    'aiModelsLastSync': localStorage.getItem('aiModelsLastSync') || 'not found',
                    'aiModelsInitialized': localStorage.getItem('aiModelsInitialized') || 'not found'
                },
                '网络状态': navigator.onLine ? 'online' : 'offline',
                '云开发配置': {
                    'env': 'cloud1-4g85f8xlb8166ff1',
                    'region': 'ap-shanghai'
                }
            };
            
            diagnosticsDiv.innerHTML = JSON.stringify(info, null, 2);
        }

        // 页面加载时自动检测连接
        window.onload = function() {
            testConnection();
            log('🚀 云函数连接测试工具已启动', 'info');
        };
    </script>
</body>
</html>
