/**
 * 云开发初始化助手
 * 解决各种云开发初始化问题
 */

class CloudInitHelper {
  constructor() {
    this.maxRetries = 5
    this.retryDelay = 2000
    this.initialized = false
    this.lastError = null
  }

  /**
   * 强制重新初始化云开发
   */
  async forceReinit() {
    console.log('🔄 开始强制重新初始化云开发...')
    
    try {
      // 清除所有可能的缓存状态
      this.clearCloudCache()
      
      // 使用多种配置尝试初始化
      const configs = this.getInitConfigs()
      
      for (let i = 0; i < configs.length; i++) {
        const config = configs[i]
        console.log(`🔄 尝试配置 ${i + 1}:`, config)
        
        const success = await this.tryInitWithConfig(config)
        if (success) {
          console.log('✅ 云开发初始化成功！')
          this.initialized = true
          return { success: true, config }
        }
      }
      
      throw new Error('所有配置尝试均失败')
      
    } catch (error) {
      console.error('❌ 强制重新初始化失败:', error)
      this.lastError = error
      return { success: false, error: error.message }
    }
  }

  /**
   * 清除云开发缓存
   */
  clearCloudCache() {
    try {
      // 清除wx.cloud的内部状态
      if (typeof wx !== 'undefined' && wx.cloud) {
        delete wx.cloud._initialized
        delete wx.cloud._config
        delete wx.cloud._database
        
        // 如果有其他内部属性，也清除
        Object.keys(wx.cloud).forEach(key => {
          if (key.startsWith('_')) {
            delete wx.cloud[key]
          }
        })
      }
      
      console.log('🧹 云开发缓存已清除')
    } catch (error) {
      console.warn('⚠️ 清除缓存时出现警告:', error)
    }
  }

  /**
   * 获取初始化配置列表
   */
  getInitConfigs() {
    return [
      // 标准配置
      {
        env: 'cloud1-4g85f8xlb8166ff1',
        traceUser: true,
        timeout: 60000
      },
      // 简化配置
      {
        env: 'cloud1-4g85f8xlb8166ff1',
        traceUser: false
      },
      // 最小配置
      {
        env: 'cloud1-4g85f8xlb8166ff1'
      },
      // 带region的配置
      {
        env: 'cloud1-4g85f8xlb8166ff1',
        region: 'ap-shanghai',
        traceUser: true,
        timeout: 30000
      }
    ]
  }

  /**
   * 尝试使用指定配置初始化
   */
  async tryInitWithConfig(config) {
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`🔄 第${attempt}次尝试初始化...`)
        
        // 初始化云开发
        wx.cloud.init(config)
        
        // 等待一下让初始化完成
        await this.sleep(1000)

        const testResult = await this.testDatabaseConnection()
        if (testResult.success) {
          console.log(`✅ 第${attempt}次尝试成功！`)
          return true
        } else {
          throw new Error(testResult.error)
        }
        
      } catch (error) {
        console.error(`❌ 第${attempt}次尝试失败:`, error)
        
        if (attempt < this.maxRetries) {
          console.log(`⏳ 等待${this.retryDelay}ms后重试...`)
          await this.sleep(this.retryDelay)
        }
      }
    }
    
    return false
  }

  /**
   * 测试数据库连接
   */
  async testDatabaseConnection() {
    try {
      console.log('🔍 测试数据库连接...')
      
      const db = wx.cloud.database()
      
      // 尝试简单的查询操作
      const result = await db.collection('users').limit(1).get()
      
      console.log('✅ 数据库连接测试成功')
      return { success: true, result }
      
    } catch (error) {
      console.error('❌ 数据库连接测试失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 诊断云开发环境
   */
  async diagnoseEnvironment() {
    console.log('🔍 开始诊断云开发环境...')
    
    const diagnosis = {
      timestamp: new Date().toISOString(),
      platform: this.getPlatformInfo(),
      cloudSupport: this.checkCloudSupport(),
      networkStatus: await this.checkNetworkStatus(),
      environmentTest: await this.testEnvironmentAccess(),
      suggestions: []
    }
    
    // 生成建议
    diagnosis.suggestions = this.generateSuggestions(diagnosis)
    
    console.log('📋 诊断完成:', diagnosis)
    return diagnosis
  }

  /**
   * 获取平台信息
   */
  getPlatformInfo() {
    try {
      if (typeof wx !== 'undefined') {
        const systemInfo = wx.getSystemInfoSync()
        return {
          platform: systemInfo.platform,
          version: systemInfo.version,
          SDKVersion: systemInfo.SDKVersion,
          brand: systemInfo.brand,
          model: systemInfo.model
        }
      }
      return { platform: 'unknown' }
    } catch (error) {
      return { platform: 'error', error: error.message }
    }
  }

  /**
   * 检查云开发支持
   */
  checkCloudSupport() {
    return {
      wxExists: typeof wx !== 'undefined',
      cloudExists: typeof wx !== 'undefined' && !!wx.cloud,
      cloudInit: typeof wx !== 'undefined' && !!wx.cloud && typeof wx.cloud.init === 'function',
      cloudDatabase: typeof wx !== 'undefined' && !!wx.cloud && typeof wx.cloud.database === 'function'
    }
  }

  /**
   * 检查网络状态
   */
  async checkNetworkStatus() {
    try {
      if (typeof wx !== 'undefined' && wx.getNetworkType) {
        const networkInfo = await new Promise((resolve, reject) => {
          wx.getNetworkType({
            success: resolve,
            fail: reject
          })
        })
        return { success: true, networkType: networkInfo.networkType }
      }
      return { success: false, error: 'API不可用' }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * 测试环境访问
   */
  async testEnvironmentAccess() {
    const tests = []

    const envIds = [
      'cloud1-4g85f8xlb8166ff1',
      'cloud1-4g85f8xlb8166ff1-dev',
      'cloud1-4g85f8xlb8166ff1-test'
    ]
    
    for (const envId of envIds) {
      try {
        wx.cloud.init({ env: envId })
        const db = wx.cloud.database()
        await db.collection('users').limit(1).get()
        
        tests.push({ envId, success: true })
      } catch (error) {
        tests.push({ envId, success: false, error: error.message })
      }
    }
    
    return tests
  }

  /**
   * 生成修复建议
   */
  generateSuggestions(diagnosis) {
    const suggestions = []
    
    if (!diagnosis.cloudSupport.wxExists) {
      suggestions.push('当前环境不支持微信小程序API')
    }
    
    if (!diagnosis.cloudSupport.cloudExists) {
      suggestions.push('云开发API不可用，请检查基础库版本')
    }
    
    if (diagnosis.networkStatus && !diagnosis.networkStatus.success) {
      suggestions.push('网络连接异常，请检查网络设置')
    }
    
    if (diagnosis.environmentTest && diagnosis.environmentTest.every(test => !test.success)) {
      suggestions.push('所有环境ID测试均失败，请检查云开发环境配置')
    }
    
    suggestions.push('尝试重启微信开发者工具')
    suggestions.push('检查云开发控制台中的环境状态')
    
    return suggestions
  }

  /**
   * 延时函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 获取初始化状态
   */
  getStatus() {
    return {
      initialized: this.initialized,
      lastError: this.lastError,
      timestamp: new Date().toISOString()
    }
  }
}

// 创建全局实例
const cloudInitHelper = new CloudInitHelper()

module.exports = {
  cloudInitHelper,
  CloudInitHelper
}
