<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试AI模型显示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .result-area {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Monaco', '<PERSON>lo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .model-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            background: #fff;
        }
        .model-header {
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 10px;
        }
        .model-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: white;
        }
        .status-active { background-color: #52c41a; }
        .status-inactive { background-color: #d9d9d9; color: #666; }
    </style>
</head>
<body>
    <h1>🚀 快速测试AI模型显示</h1>
    
    <div class="test-card">
        <h2>📊 测试结果</h2>
        <div style="margin-bottom: 15px;">
            <button class="test-button" onclick="testGetModels()">测试获取AI模型</button>
            <button class="test-button" onclick="testDirectQuery()">直接查询数据库</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
        </div>
        <div id="testResults" class="result-area"></div>
    </div>

    <div class="test-card">
        <h2>🎯 模型列表</h2>
        <div id="modelsList"></div>
    </div>

    <script src="https://web.sdk.qcloud.com/tcb-js-sdk/1.19.2/tcb.js"></script>
    <script>
        let cloudbaseApp = null;
        let testResults = '';

        // 等待SDK加载完成
        function waitForSDK() {
            return new Promise((resolve, reject) => {
                if (typeof tcb !== 'undefined') {
                    resolve();
                    return;
                }

                let attempts = 0;
                const checkSDK = () => {
                    attempts++;
                    if (typeof tcb !== 'undefined') {
                        resolve();
                    } else if (attempts > 50) {
                        reject(new Error('SDK加载超时'));
                    } else {
                        setTimeout(checkSDK, 100);
                    }
                };
                checkSDK();
            });
        }

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            testResults += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            document.getElementById('testResults').innerHTML = testResults;
            document.getElementById('testResults').scrollTop = document.getElementById('testResults').scrollHeight;
        }

        function clearResults() {
            testResults = '';
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('modelsList').innerHTML = '';
        }

        async function initCloudbase() {
            try {
                // 等待SDK加载
                await waitForSDK();

                if (cloudbaseApp) return cloudbaseApp;

                cloudbaseApp = tcb.init({
                    env: 'cloud1-4g85f8xlb8166ff1',
                    region: 'ap-shanghai'
                });

                const auth = cloudbaseApp.auth();
                await auth.signInAnonymously();

                log('✅ 云开发SDK初始化成功', 'success');
                return cloudbaseApp;
            } catch (error) {
                log('❌ 云开发SDK初始化失败: ' + error.message, 'error');
                throw error;
            }
        }

        async function testGetModels() {
            log('🔄 开始测试getModels API...', 'info');
            
            try {
                const app = await initCloudbase();
                const result = await app.callFunction({
                    name: 'adminAPI',
                    data: { action: 'ai.getModels' }
                });

                log('📊 API调用结果:', 'success');
                log('返回状态: ' + (result.result?.code || '未知'), 'info');
                
                if (result.result && result.result.code === 200) {
                    const models = result.result.data;
                    log(`✅ 成功获取 ${models.length} 个AI模型`, 'success');
                    
                    // 显示详细信息
                    models.forEach((model, index) => {
                        log(`${index + 1}. ${model.name} (${model.id})`, 'info');
                        log(`   提供商: ${model.provider}`, 'info');
                        log(`   模型: ${model.model}`, 'info');
                        log(`   状态: ${model.status}`, 'info');
                        log(`   API密钥: ${model.apiKey ? '已设置' : '未设置'}`, 'info');
                    });
                    
                    // 显示模型卡片
                    displayModels(models);
                    
                } else {
                    log('❌ API调用失败: ' + JSON.stringify(result.result), 'error');
                }
                
            } catch (error) {
                log('❌ 测试失败: ' + error.message, 'error');
            }
        }

        async function testDirectQuery() {
            log('🔄 开始直接查询数据库...', 'info');
            
            try {
                const app = await initCloudbase();
                const db = app.database();
                
                const result = await db.collection('ai_configs').limit(100).get();
                
                log('📊 数据库查询结果:', 'success');
                log(`找到 ${result.data.length} 条记录`, 'info');
                
                if (result.data.length > 0) {
                    result.data.forEach((item, index) => {
                        log(`${index + 1}. ${item.name || '未命名'} (${item._id})`, 'info');
                        log(`   提供商: ${item.provider || '未设置'}`, 'info');
                        log(`   模型: ${item.model || '未设置'}`, 'info');
                        log(`   状态: ${item.status || '未设置'}`, 'info');
                        log(`   创建时间: ${item.createTime || '未设置'}`, 'info');
                    });
                } else {
                    log('⚠️ 数据库中没有找到ai_configs数据', 'warning');
                }
                
            } catch (error) {
                log('❌ 数据库查询失败: ' + error.message, 'error');
            }
        }

        function displayModels(models) {
            const modelsDiv = document.getElementById('modelsList');
            
            if (!models || models.length === 0) {
                modelsDiv.innerHTML = '<p>没有找到AI模型</p>';
                return;
            }
            
            let html = '';
            models.forEach(model => {
                const statusClass = model.status === 'active' ? 'status-active' : 'status-inactive';
                
                html += `
                    <div class="model-card">
                        <div class="model-header">
                            ${model.name}
                            <span class="model-status ${statusClass}">${model.status}</span>
                        </div>
                        <div><strong>ID:</strong> ${model.id}</div>
                        <div><strong>提供商:</strong> ${model.provider}</div>
                        <div><strong>模型:</strong> ${model.model}</div>
                        <div><strong>API密钥:</strong> ${model.apiKey ? '已设置' : '未设置'}</div>
                        <div><strong>基础URL:</strong> ${model.baseUrl || '未设置'}</div>
                        <div><strong>输入价格:</strong> ¥${model.inputPrice || 0}/1K tokens</div>
                        <div><strong>输出价格:</strong> ¥${model.outputPrice || 0}/1K tokens</div>
                        <div><strong>创建时间:</strong> ${model.createdAt || '未知'}</div>
                    </div>
                `;
            });
            
            modelsDiv.innerHTML = html;
        }

        // 页面加载时自动测试
        window.onload = function() {
            log('🚀 快速测试工具已启动', 'info');
            log('💡 点击"测试获取AI模型"按钮开始测试', 'info');
            
            // 自动执行测试
            setTimeout(() => {
                testGetModels();
            }, 1000);
        };
    </script>
</body>
</html>
