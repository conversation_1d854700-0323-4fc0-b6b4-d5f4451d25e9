/**
 * 学生添加页面 - 简单版本
 */
const app = getApp();
const { cloudService } = require('../../../services/cloudService');
// 学生导入模板工具函数
const StudentImportUtils = {
  /**
   * 生成CSV内容
   */
  createCSVContent() {
    const templateData = [
      ['姓名*', '学号*', '班级*', '性别', '联系电话', '家长电话', '地址', '备注'],
      ['', '', '', '', '', '', '', ''],
      ['', '', '', '', '', '', '', ''],
      ['', '', '', '', '', '', '', ''],
      ['', '', '', '', '', '', '', ''],
      ['', '', '', '', '', '', '', ''],
      ['', '', '', '', '', '', '', ''],
      ['', '', '', '', '', '', '', '']
    ];

    // 使用标准CSV格式：逗号分隔，字段用引号包围
    return templateData.map(row =>
      row.map(cell => `"${cell}"`).join(',')
    ).join('\n');
  },

  /**
   * 生成文件名
   */
  generateFileName() {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10); // YYYY-MM-DD
    const timeStr = now.toTimeString().slice(0, 5).replace(':', ''); // HHMM
    return `学生批量导入模板_${dateStr}_${timeStr}.csv`;
  },

  /**
   * 解析CSV内容
   */
  parseCSVContent(content) {
    const lines = content.split('\n').filter(line => line.trim());

    if (lines.length < 2) {
      throw new Error('文件内容不足，至少需要表头和一行数据');
    }

    const students = [];
    const errors = [];

    // 跳过表头，从第二行开始解析
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // 解析CSV行（处理引号包围的字段）
      const values = this.parseCSVLine(line);

      // 只处理有姓名的行
      if (values[0] && values[0].trim()) {
        const student = {
          name: values[0] ? values[0].trim() : '',
          studentNumber: values[1] ? values[1].trim() : '',
          className: values[2] ? values[2].trim() : '',
          gender: values[3] ? values[3].trim() : '男',
          phone: values[4] ? values[4].trim() : '',
          parentPhone: values[5] ? values[5].trim() : '',
          address: values[6] ? values[6].trim() : '',
          remark: values[7] ? values[7].trim() : ''
        };

        // 数据验证
        const validation = this.validateStudentData(student, i + 1);
        if (validation.isValid) {
          students.push(student);
        } else {
          errors.push(`第${i + 1}行: ${validation.errors.join(', ')}`);
        }
      }
    }

    return {
      students: students,
      errors: errors,
      totalRows: lines.length - 1,
      validRows: students.length,
      errorRows: errors.length
    };
  },

  /**
   * 解析CSV行（处理引号包围的字段）
   */
  parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
      const char = line[i];

      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // 双引号转义
          current += '"';
          i += 2;
        } else {
          // 切换引号状态
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === ',' && !inQuotes) {
        // 字段分隔符
        result.push(current);
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }

    // 添加最后一个字段
    result.push(current);

    return result;
  },

  /**
   * 验证学生数据
   */
  validateStudentData(student, rowNumber) {
    const errors = [];

    // 必填字段验证
    if (!student.name || student.name.trim() === '') {
      errors.push('姓名不能为空');
    } else {
      if (student.name.length > 20) {
        errors.push('姓名不能超过20个字符');
      }
      if (!/^[\u4e00-\u9fa5a-zA-Z\s]+$/.test(student.name)) {
        errors.push('姓名只能包含中文、英文和空格');
      }
    }

    if (!student.studentNumber || student.studentNumber.trim() === '') {
      errors.push('学号不能为空');
    } else if (student.studentNumber.length > 20) {
      errors.push('学号不能超过20个字符');
    }

    if (!student.className || student.className.trim() === '') {
      errors.push('班级不能为空');
    }

    // 可选字段验证
    if (student.gender && !['男', '女'].includes(student.gender)) {
      errors.push('性别只能填写"男"或"女"');
    }

    if (student.phone && !this.isValidPhone(student.phone)) {
      errors.push('联系电话格式不正确（应为11位手机号）');
    }

    if (student.parentPhone && !this.isValidPhone(student.parentPhone)) {
      errors.push('家长电话格式不正确（应为11位手机号）');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  },

  /**
   * 验证手机号格式
   */
  isValidPhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone);
  },

  /**
   * 检查学号重复
   */
  checkDuplicateStudentNumbers(students) {
    const studentNumbers = {};
    const duplicates = [];

    students.forEach((student, index) => {
      if (studentNumbers[student.studentNumber]) {
        duplicates.push({
          studentNumber: student.studentNumber,
          rows: [studentNumbers[student.studentNumber], index + 1]
        });
      } else {
        studentNumbers[student.studentNumber] = index + 1;
      }
    });

    return duplicates;
  },

  /**
   * 转换为数据库格式
   */
  convertToDBFormat(student) {
    return {
      name: student.name,
      studentId: student.studentNumber,
      className: student.className,
      gender: student.gender === '女' ? 'female' : 'male',
      genderText: student.gender,
      phone: student.phone,
      parentPhone: student.parentPhone,
      address: student.address,
      remark: student.remark
    };
  }
};

Page({
  data: {
    mode: 'single',
    isEdit: false,
    studentId: '',
    formData: {
      name: '',
      studentNumber: '',
      className: '',
      gender: '',
      genderText: ''
    },
    errors: {},
    hasShown: false,
    submitting: false,
    
    // 批量导入相关字段
    uploadedFile: null,
    previewData: [],
    validCount: 0,
    errorCount: 0,
    importing: false,
    showFilePreview: false,
    showImportResult: false,
    importResultMessage: ''
  },

  /**
   * 页面加载
   */
  async onLoad(options) {
    const { mode, id } = options;

    wx.setNavigationBarTitle({
      title: mode === 'edit' ? '编辑学生' : '添加学生'
    });

    this.setData({
      errors: {}
    });

    if (mode === 'edit' && id) {
      this.setData({
        isEdit: true,
        studentId: id
      });
      this.loadStudentData(id);
    }
  },

  /**
   * 加载学生数据（编辑模式）
   */
  async loadStudentData(studentId) {
    try {
      wx.showLoading({
        title: '加载学生信息...',
        mask: true
      });

      // 获取云服务实例
      const cloudService = app.globalData.cloudService;
      if (!cloudService) {
        throw new Error('云服务未初始化');
      }

      // 获取学生详细信息
      const result = await cloudService.getStudentDetail(studentId);
      
      if (result.success && result.data) {
        const student = result.data;
        
        // 填充表单数据
        this.setData({
          formData: {
            name: student.name || '',
            studentNumber: student.studentId || '',  // 数据库中字段名是studentId
            className: student.className || '',
            gender: student.gender || '',
            genderText: student.genderText || student.gender || ''  // 优先使用genderText（中文）
          }
        });

      } else {
        throw new Error(result.error || '获取学生信息失败');
      }

    } catch (error) {
      console.error('加载学生数据失败:', error);
      wx.showToast({
        title: '加载学生信息失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 切换模式
   */
  switchMode(e) {
    const { mode } = e.currentTarget.dataset;
    this.setData({ mode });
  },

  /**
   * 姓名输入变化
   */
  onNameChange(e) {
    const value = e.detail.value;
    this.setData({
      'formData.name': value,
      'errors.name': ''
    });
  },

  /**
   * 学号输入变化
   */
  onStudentNumberChange(e) {
    const value = e.detail.value;
    this.setData({
      'formData.studentNumber': value,
      'errors.studentNumber': ''
    });
  },

  /**
   * 班级输入变化
   */
  onClassNameChange(e) {
    const value = e.detail.value;
    this.setData({
      'formData.className': value,
      'errors.className': ''
    });
  },

  /**
   * 性别选择
   */
  showGenderPicker() {
    wx.showActionSheet({
      itemList: ['男', '女'],
      success: (res) => {
        const genderText = res.tapIndex === 0 ? '男' : '女';
        const genderValue = res.tapIndex === 0 ? 'male' : 'female';

        this.setData({
          'formData.gender': genderValue,
          'formData.genderText': genderText
        });
      }
    });
  },

  /**
   * 下载导入模板
   */
  downloadTemplate() {
    this.generateCSVTemplate();
  },

  /**
   * 生成CSV模板
   */
  generateCSVTemplate() {
    wx.showLoading({
      title: '生成模板中...',
      mask: true
    });

    try {
      // 生成CSV内容
      const csvContent = StudentImportUtils.createCSVContent();
      const fileName = StudentImportUtils.generateFileName();

      // 创建文件
      const fs = wx.getFileSystemManager();
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;
      fs.writeFileSync(filePath, csvContent, 'utf8');

      wx.hideLoading();

      // 显示生成成功提示
      wx.showModal({
        title: '模板生成成功',
        content: `文件名：${fileName}\n\n模板包含8个字段：\n• 姓名、学号、班级（必填）\n• 性别、联系电话、家长电话\n• 地址、备注（选填）\n\n是否立即分享到微信？`,
        confirmText: '立即分享',
        cancelText: '稍后处理',
        success: (res) => {
          if (res.confirm) {
            this.shareTemplateToWeChat(filePath, fileName);
          } else {
            wx.showToast({
              title: '模板已保存',
              icon: 'success',
              duration: 2000
            });
          }
        },
        fail: () => {
          // 备用方案
          wx.showToast({
            title: '模板已生成',
            icon: 'success',
            duration: 3000
          });
          setTimeout(() => {
            this.showSimpleShareOptions(filePath, fileName);
          }, 1000);
        }
      });

    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '生成失败',
        content: `生成模板时出现错误，请重试。\n\n如果问题持续存在，请检查：\n• 存储空间是否充足\n• 小程序权限是否正常`,
        showCancel: false,
        confirmText: '重新尝试',
        success: () => {
          this.downloadTemplate();
        }
      });
    }
  },

  /**
   * 显示简单的分享选项（备用方案）
   */
  showSimpleShareOptions(filePath, fileName) {
    wx.showActionSheet({
      itemList: ['分享到微信', '查看文件信息', '重新生成'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.shareTemplateToWeChat(filePath, fileName);
            break;
          case 1:
            this.showFileInfo(filePath, fileName);
            break;
          case 2:
            this.downloadTemplate();
            break;
        }
      },
      fail: () => {
        wx.showToast({
          title: '模板已生成',
          icon: 'success',
          duration: 3000
        });
      }
    });
  },

  /**
   * 显示文件信息
   */
  showFileInfo(filePath, fileName) {
    wx.showModal({
      title: '文件信息',
      content: `文件名：${fileName}\n\n文件已保存到小程序临时目录\n可通过分享功能发送到微信`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 分享模板到微信
   */
  shareTemplateToWeChat(filePath, fileName) {
    // 检查分享功能是否可用
    if (typeof wx.shareFileMessage !== 'function') {
      wx.showModal({
        title: '功能不可用',
        content: '当前环境不支持文件分享\n请在真机微信中使用\n\n您也可以手动创建Excel文件\n按照模板格式填写数据',
        showCancel: false,
        confirmText: '知道了'
      });
      return;
    }

    // 显示分享指引
    wx.showModal({
      title: '分享文件到微信',
      content: `文件：${fileName}\n\n操作步骤：\n1. 选择微信聊天发送文件\n2. 在微信中下载并打开\n3. 用Excel编辑后保存\n4. 返回小程序导入数据\n\n建议发送到"文件传输助手"`,
      confirmText: '开始分享',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performFileShare(filePath, fileName);
        }
      }
    });
  },

  /**
   * 执行文件分享
   */
  performFileShare(filePath, fileName) {
    wx.shareFileMessage({
      filePath: filePath,
      fileName: fileName,
      success: () => {
        wx.showModal({
          title: '分享成功',
          content: `文件已发送到微信聊天\n\n后续操作：\n1. 在微信中下载文件\n2. 用Excel打开编辑\n3. 填写学生信息并保存\n4. 返回小程序导入数据\n\n请按模板格式准确填写`,
          showCancel: false,
          confirmText: '知道了'
        });
      },
      fail: (err) => {
        wx.showModal({
          title: '分享失败',
          content: `无法分享文件到微信\n\n可能原因：\n• 微信版本过低\n• 系统权限限制\n• 文件路径异常\n\n建议更新微信版本或重新生成模板`,
          showCancel: false,
          confirmText: '重新尝试',
          success: () => {
            this.downloadTemplate();
          }
        });
      }
    });
  },

  /**
   * 选择文件导入
   */
  chooseFile() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['csv', 'xlsx', 'xls'],
      success: (res) => {
        const file = res.tempFiles[0];
        this.parseFile(file);
      },
      fail: (err) => {
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 解析文件
   */
  parseFile(file) {
    wx.showLoading({
      title: '解析文件中...',
      mask: true
    });

    try {
      const fs = wx.getFileSystemManager();
      const content = fs.readFileSync(file.path, 'utf8');
      
      // 使用工具函数解析CSV内容
      const parseResult = StudentImportUtils.parseCSVContent(content);

      if (parseResult.errors.length > 0) {
        const errorMessage = parseResult.errors.slice(0, 5).join('\n') +
          (parseResult.errors.length > 5 ? `\n...还有${parseResult.errors.length - 5}个错误` : '');

        wx.showModal({
          title: '❌ 数据验证失败',
          content: `发现${parseResult.errors.length}个错误：\n\n${errorMessage}\n\n请修正后重新导入`,
          showCancel: false,
          confirmText: '我知道了'
        });
        return;
      }

      const students = parseResult.students;

      // 检查学号重复
      const duplicates = StudentImportUtils.checkDuplicateStudentNumbers(students);
      if (duplicates.length > 0) {
        const duplicateMessage = duplicates.map(dup =>
          `学号"${dup.studentNumber}"在第${dup.rows.join('、')}行重复`
        ).join('\n');

        wx.showModal({
          title: '❌ 学号重复',
          content: `发现重复学号：\n\n${duplicateMessage}\n\n请修正后重新导入`,
          showCancel: false,
          confirmText: '我知道了'
        });
        return;
      }

      wx.hideLoading();

      if (students.length === 0) {
        wx.showToast({
          title: '未找到有效的学生数据',
          icon: 'none'
        });
        return;
      }

      // 更新预览数据
      this.setData({
        uploadedFile: {
          name: file.name,
          size: this.formatFileSize(file.size)
        },
        previewData: students,
        validCount: students.length,
        errorCount: 0
      });

      // 确认导入
      wx.showModal({
        title: '✅ 文件解析成功',
        content: `解析到 ${students.length} 个学生信息，数据格式正确！\n\n您可以：\n• 点击"预览"查看详细数据\n• 点击"导入学生"开始批量导入`,
        confirmText: '开始导入',
        cancelText: '先预览',
        success: (res) => {
          if (res.confirm) {
            this.onBatchImport();
          } else {
            this.setData({
              showFilePreview: true
            });
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '文件格式错误',
        icon: 'none'
      });
    }
  },

  /**
   * 批量导入学生
   */
  async importStudents(students) {
    wx.showLoading({
      title: `导入中 0/${students.length}`,
      mask: true
    });

    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < students.length; i++) {
      const student = students[i];
      
      wx.showLoading({
        title: `导入中 ${i + 1}/${students.length}`,
        mask: true
      });

      try {
        // 使用工具函数转换为数据库格式
        const studentData = StudentImportUtils.convertToDBFormat(student);

        // 使用统一数据管理器添加学生，确保数据同步
        const { addStudent } = require('../../../utils/unifiedDataManager');
        const result = await addStudent(studentData, 'student-batch-import');
        
        if (result.success) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        failCount++;
      }

      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    wx.hideLoading();

    // 显示导入结果
    wx.showModal({
      title: '导入完成',
      content: `成功导入：${successCount}个学生\n失败：${failCount}个学生`,
      showCancel: false,
      confirmText: '确定',
      success: () => {
        if (successCount > 0) {
          // 导入成功，返回学生列表
          wx.navigateBack();
        }
      }
    });
  },

  /**
   * 提交表单
   */
  async onSubmit() {
    const { formData, isEdit, studentId } = this.data;

    // 验证必填项
    const errors = {};

    if (!formData.name || !formData.name.trim()) {
      errors.name = '请输入学生姓名';
    }

    if (!formData.studentNumber || !formData.studentNumber.trim()) {
      errors.studentNumber = '请输入学号';
    }

    if (!formData.className || !formData.className.trim()) {
      errors.className = '请输入班级名称';
    }

    if (Object.keys(errors).length > 0) {
      this.setData({ errors });
      wx.showToast({
        title: '请检查表单信息',
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({ submitting: true });

      // 准备学生数据
      const studentData = {
        name: formData.name.trim(),
        studentId: formData.studentNumber.trim(),
        className: formData.className.trim(),
        gender: formData.gender || 'male',
        genderText: formData.genderText || '男'
      };

      let result;
      if (isEdit && studentId) {
        result = await cloudService.updateStudent(studentId, studentData);
      } else {
        // 使用统一数据管理器添加学生，确保数据同步
        const { addStudent } = require('../../../utils/unifiedDataManager');
        result = await addStudent(studentData, 'student-create');
      }

      if (result.success) {
        wx.showToast({
          title: isEdit ? '保存成功' : '添加成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(result.error || '操作失败');
      }

    } catch (error) {
      wx.showToast({
        title: isEdit ? '保存失败' : '添加失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * 批量导入按钮点击事件
   */
  onBatchImport() {
    if (this.data.importing) {
      return;
    }

    const { previewData, validCount, errorCount } = this.data;
    
    if (previewData.length === 0) {
      wx.showToast({
        title: '请先选择导入文件',
        icon: 'none'
      });
      return;
    }

    if (errorCount > 0) {
      wx.showModal({
        title: '数据验证失败',
        content: `发现 ${errorCount} 条错误记录，请修正后重新上传文件`,
        showCancel: false,
        confirmText: '我知道了'
      });
      return;
    }

    // 确认导入
    wx.showModal({
      title: '确认批量导入',
      content: `即将导入 ${validCount} 名学生，确定要继续吗？\n\n导入后将无法撤销此操作。`,
      confirmText: '确认导入',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.importStudents(previewData);
        }
      }
    });
  },

  /**
   * 文件预览
   */
  previewFile() {
    this.setData({
      showFilePreview: true
    });
  },

  /**
   * 隐藏文件预览
   */
  hideFilePreview() {
    this.setData({
      showFilePreview: false
    });
  },

  /**
   * 移除已上传文件
   */
  removeFile() {
    this.setData({
      uploadedFile: null,
      previewData: [],
      validCount: 0,
      errorCount: 0,
      showFilePreview: false
    });
  },

  /**
   * 隐藏导入结果
   */
  hideImportResult() {
    this.setData({
      showImportResult: false,
      importResultMessage: ''
    });
  },

  /**
   * 取消操作
   */
  onCancel() {
    wx.navigateBack();
  }
});