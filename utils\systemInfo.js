/**
 * 系统信息获取工具 - 使用新版API
 * 替换已过时的 wx.getSystemInfoSync()
 */

class SystemInfoHelper {
  constructor() {
    this.cachedInfo = null;
  }

  /**
   * 获取完整系统信息（兼容新旧API）
   */
  getSystemInfo() {
    if (this.cachedInfo) {
      return this.cachedInfo;
    }

    try {
      // 使用新版API组合获取完整信息
      const deviceInfo = wx.getDeviceInfo();
      const windowInfo = wx.getWindowInfo();
      const appBaseInfo = wx.getAppBaseInfo();
      const systemSetting = wx.getSystemSetting();
      const appAuthorizeSetting = wx.getAppAuthorizeSetting();

      this.cachedInfo = {
        // 设备信息
        brand: deviceInfo.brand,
        model: deviceInfo.model,
        system: deviceInfo.system,
        platform: deviceInfo.platform,
        benchmarkLevel: deviceInfo.benchmarkLevel,
        
        // 窗口信息
        pixelRatio: windowInfo.pixelRatio,
        screenWidth: windowInfo.screenWidth,
        screenHeight: windowInfo.screenHeight,
        windowWidth: windowInfo.windowWidth,
        windowHeight: windowInfo.windowHeight,
        statusBarHeight: windowInfo.statusBarHeight,
        safeArea: windowInfo.safeArea,
        
        // 应用基础信息
        SDKVersion: appBaseInfo.SDKVersion,
        enableDebug: appBaseInfo.enableDebug,
        host: appBaseInfo.host,
        language: appBaseInfo.language,
        version: appBaseInfo.version,
        theme: appBaseInfo.theme,
        
        // 系统设置
        bluetoothEnabled: systemSetting.bluetoothEnabled,
        locationEnabled: systemSetting.locationEnabled,
        wifiEnabled: systemSetting.wifiEnabled,
        deviceOrientation: systemSetting.deviceOrientation,
        
        // 授权设置
        albumAuthorized: appAuthorizeSetting.albumAuthorized,
        bluetoothAuthorized: appAuthorizeSetting.bluetoothAuthorized,
        cameraAuthorized: appAuthorizeSetting.cameraAuthorized,
        locationAuthorized: appAuthorizeSetting.locationAuthorized,
        locationReducedAccuracy: appAuthorizeSetting.locationReducedAccuracy,
        microphoneAuthorized: appAuthorizeSetting.microphoneAuthorized,
        notificationAuthorized: appAuthorizeSetting.notificationAuthorized
      };

      return this.cachedInfo;
    } catch (error) {

      // 降级到旧版API
      try {
        this.cachedInfo = wx.getSystemInfoSync();
        return this.cachedInfo;
      } catch (fallbackError) {
        console.error('[SystemInfo] 获取系统信息完全失败:', fallbackError);
        
        // 返回默认值
        return {
          platform: 'unknown',
          system: 'unknown',
          version: 'unknown',
          SDKVersion: 'unknown',
          screenWidth: 375,
          screenHeight: 667,
          windowWidth: 375,
          windowHeight: 667,
          pixelRatio: 2,
          statusBarHeight: 20,
          language: 'zh_CN',
          theme: 'light'
        };
      }
    }
  }

  /**
   * 获取平台信息
   */
  getPlatform() {
    return this.getSystemInfo().platform || 'unknown';
  }

  /**
   * 判断是否为开发工具
   */
  isDevTools() {
    return this.getPlatform() === 'devtools';
  }

  /**
   * 判断是否为真机环境
   */
  isMobile() {
    const platform = this.getPlatform();
    return platform !== 'devtools' && platform !== 'unknown';
  }

  /**
   * 获取设备型号
   */
  getDeviceModel() {
    const info = this.getSystemInfo();
    return `${info.brand || 'Unknown'} ${info.model || 'Device'}`;
  }

  /**
   * 清除缓存（当需要获取最新信息时）
   */
  clearCache() {
    this.cachedInfo = null;
  }
}

// 创建全局实例
const systemInfoHelper = new SystemInfoHelper();

module.exports = {
  SystemInfoHelper,
  systemInfoHelper,
  
  // 兼容性方法（替换 wx.getSystemInfoSync）
  getSystemInfo: () => systemInfoHelper.getSystemInfo(),
  getPlatform: () => systemInfoHelper.getPlatform(),
  isDevTools: () => systemInfoHelper.isDevTools(),
  isMobile: () => systemInfoHelper.isMobile()
};