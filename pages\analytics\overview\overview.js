/**
 * 数据分析概览页面
 * 基于微信云开发
 */
const { cloudService } = require('../../../services/cloudService');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 时间筛选器
    timeFilters: [
      { name: '本周', value: 'week' },
      { name: '本月', value: 'month' },
      { name: '本学期', value: 'semester' },
      { name: '全部', value: 'all' }
    ],
    selectedTimeFilter: 'month',

    // 核心指标（从云数据库计算）
    metrics: {
      totalStudents: 0,
      studentGrowth: 0,
      totalRecords: 0,
      recordGrowth: 0,
      avgScore: 0,
      scoreChange: 0,
      totalComments: 0,
      commentGrowth: 0
    },

    // 行为统计（从云数据库计算）
    behaviorStats: [
      {
        type: 'positive',
        name: '积极行为',
        icon: 'like-o',
        count: 0,
        percentage: 0
      },
      {
        type: 'academic',
        name: '学习表现',
        icon: 'edit',
        count: 0,
        percentage: 0
      },
      {
        type: 'social',
        name: '社交表现',
        icon: 'friends-o',
        count: 0,
        percentage: 0
      },
      {
        type: 'negative',
        name: '需改进',
        icon: 'warning-o',
        count: 0,
        percentage: 0
      }
    ],

    // 班级排行（从云数据库计算）
    classRanking: [],

    // 记录趋势数据
    recordTrend: {
      labels: [], // 时间标签
      data: [], // 记录数量
      maxValue: 0 // 最大值，用于图表缩放
    },

    // 图表显示状态
    showChart: false,

    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadAnalyticsData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadAnalyticsData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 选择时间筛选器
   */
  selectTimeFilter(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({ selectedTimeFilter: value });
    this.loadAnalyticsData();
  },

  /**
   * 加载分析数据
   */
  async loadAnalyticsData() {
    try {
      this.setData({ loading: true });

      // 从云数据库获取真实数据
      const [studentsResult, recordsResult, commentsResult, classesResult] = await Promise.all([
        cloudService.getStudentList(),
        cloudService.getRecordList({ pageSize: 1000 }),
        cloudService.getCommentList ? cloudService.getCommentList({ pageSize: 1000 }) : Promise.resolve({ success: true, data: [] }),
        cloudService.getClassList()
      ]);

      // 计算核心指标
      const totalStudents = studentsResult.success ? studentsResult.data.length : 0;
      const allRecords = recordsResult.success ? recordsResult.data : [];
      const totalRecords = allRecords.length;
      const allComments = commentsResult.success ? commentsResult.data : [];
      const totalComments = allComments.length;

      // 计算平均分数
      const recordsWithScore = allRecords.filter(r => r.score && r.score > 0);
      const avgScore = recordsWithScore.length > 0
        ? recordsWithScore.reduce((sum, r) => sum + r.score, 0) / recordsWithScore.length
        : 0;

      // 计算行为统计
      const behaviorCounts = {
        positive: allRecords.filter(r => r.behaviorType === 'positive').length,
        academic: allRecords.filter(r => r.behaviorType === 'academic').length,
        social: allRecords.filter(r => r.behaviorType === 'social').length,
        negative: allRecords.filter(r => r.behaviorType === 'negative').length
      };

      const totalBehaviors = Object.values(behaviorCounts).reduce((sum, count) => sum + count, 0);

      const behaviorStats = [
        {
          type: 'positive',
          name: '积极行为',
          icon: 'like-o',
          count: behaviorCounts.positive,
          percentage: totalBehaviors > 0 ? Math.round((behaviorCounts.positive / totalBehaviors) * 100) : 0
        },
        {
          type: 'academic',
          name: '学习表现',
          icon: 'edit',
          count: behaviorCounts.academic,
          percentage: totalBehaviors > 0 ? Math.round((behaviorCounts.academic / totalBehaviors) * 100) : 0
        },
        {
          type: 'social',
          name: '社交表现',
          icon: 'friends-o',
          count: behaviorCounts.social,
          percentage: totalBehaviors > 0 ? Math.round((behaviorCounts.social / totalBehaviors) * 100) : 0
        },
        {
          type: 'negative',
          name: '需改进',
          icon: 'warning-o',
          count: behaviorCounts.negative,
          percentage: totalBehaviors > 0 ? Math.round((behaviorCounts.negative / totalBehaviors) * 100) : 0
        }
      ];

      this.setData({
        metrics: {
          totalStudents,
          studentGrowth: 0, // 需要历史数据计算
          totalRecords,
          recordGrowth: 0, // 需要历史数据计算
          avgScore: Math.round(avgScore * 10) / 10,
          scoreChange: 0, // 需要历史数据计算
          totalComments,
          commentGrowth: 0 // 需要历史数据计算
        },
        behaviorStats,
        showChart: true,
        loading: false
      });
    } catch (error) {
      console.error('加载分析数据失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 格式化日期标签
   */
  formatDateLabel(date, type) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];

    switch (type) {
      case 'day':
        return `${month}/${day}`;
      case 'date':
        return `${month}月${day}日`;
      case 'month':
        return `${month}月`;
      default:
        return `${month}/${day}`;
    }
  },

  /**
   * 跳转到班级详情
   */
  goToClassDetail(e) {
    const { class: classInfo } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/class/detail/detail?id=${classInfo.id}`
    });
  }
});