/**
 * 记录用户协议同意状态的云函数
 * 用于法律合规和举证需要
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { 
    userId, 
    userInfo, 
    consentType, 
    consentVersion, 
    ipAddress, 
    userAgent,
    deviceInfo 
  } = event;

  if (!userId || !consentType) {
    return {
      success: false,
      error: '缺少必要参数：userId 或 consentType'
    };
  }

  try {
    // 获取微信调用上下文
    const wxContext = cloud.getWXContext();
    
    // 准备同意记录数据
    const consentRecord = {
      // 用户标识信息
      userId: userId,
      openId: wxContext.OPENID,
      unionId: wxContext.UNIONID || null,
      
      // 用户基本信息（脱敏处理）
      userNickname: userInfo?.nickName || '',
      userAvatar: userInfo?.avatarUrl || '',
      
      // 同意信息
      consentType: consentType, // 'privacy_policy' | 'user_agreement' | 'both'
      consentVersion: consentVersion || '1.0',
      consentStatus: 'agreed',
      consentTime: new Date(),
      
      // 技术信息（用于举证）
      ipAddress: ipAddress || wxContext.CLIENTIP || '',
      userAgent: userAgent || '',
      deviceInfo: deviceInfo || {},
      
      // 法律相关信息
      legalBasis: 'explicit_consent', // 明确同意
      dataProcessingPurpose: 'service_provision', // 服务提供
      retentionPeriod: '7_years', // 保存7年（法律要求）
      
      // 系统信息
      recordSource: 'miniprogram_login',
      recordVersion: '1.0',
      isActive: true,
      
      // 创建和更新时间
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 检查是否已有同意记录
    const existingConsent = await db.collection('user_consent_records')
      .where({
        userId: userId,
        consentType: consentType,
        isActive: true
      })
      .orderBy('consentTime', 'desc')
      .limit(1)
      .get();

    let result;
    
    if (existingConsent.data.length > 0) {
      // 更新现有记录
      const existingRecord = existingConsent.data[0];
      
      // 如果版本不同，创建新记录并将旧记录标记为非活跃
      if (existingRecord.consentVersion !== consentVersion) {
        // 将旧记录标记为非活跃
        await db.collection('user_consent_records')
          .doc(existingRecord._id)
          .update({
            data: {
              isActive: false,
              updatedAt: new Date(),
              deactivatedReason: 'version_updated'
            }
          });
        
        // 创建新版本记录
        result = await db.collection('user_consent_records').add({
          data: consentRecord
        });

      } else {
        // 更新现有记录的时间
        result = await db.collection('user_consent_records')
          .doc(existingRecord._id)
          .update({
            data: {
              consentTime: new Date(),
              updatedAt: new Date(),
              ipAddress: consentRecord.ipAddress,
              userAgent: consentRecord.userAgent,
              deviceInfo: consentRecord.deviceInfo
            }
          });

      }
    } else {
      // 创建新的同意记录
      result = await db.collection('user_consent_records').add({
        data: consentRecord
      });

    }

    // 同时记录到审计日志
    await db.collection('consent_audit_logs').add({
      data: {
        userId: userId,
        action: 'consent_recorded',
        consentType: consentType,
        consentVersion: consentVersion,
        timestamp: new Date(),
        ipAddress: consentRecord.ipAddress,
        userAgent: consentRecord.userAgent,
        recordId: result._id || existingConsent.data[0]._id,
        details: {
          source: 'miniprogram_login',
          method: existingConsent.data.length > 0 ? 'update' : 'create'
        }
      }
    });

    return {
      success: true,
      message: '用户同意状态记录成功',
      recordId: result._id || existingConsent.data[0]._id,
      consentTime: consentRecord.consentTime,
      consentVersion: consentVersion
    };

  } catch (error) {
    console.error('[recordUserConsent] 记录用户同意状态失败:', error);
    
    // 记录错误日志
    try {
      await db.collection('consent_error_logs').add({
        data: {
          userId: userId,
          consentType: consentType,
          error: error.message,
          stack: error.stack,
          timestamp: new Date(),
          context: event
        }
      });
    } catch (logError) {
      console.error('[recordUserConsent] 记录错误日志失败:', logError);
    }
    
    return {
      success: false,
      error: error.message || '记录用户同意状态时发生未知错误',
      details: error
    };
  }
};
