/**
 * 查询用户协议同意记录的云函数
 * 用于用户查看自己的同意历史和法律举证
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { userId, consentType, includeHistory = false } = event;

  if (!userId) {
    return {
      success: false,
      error: '缺少必要参数：userId'
    };
  }

  try {
    // 获取微信调用上下文
    const wxContext = cloud.getWXContext();
    
    // 验证用户身份（确保用户只能查看自己的记录）
    if (wxContext.OPENID) {
      const userCheck = await db.collection('user_consent_records')
        .where({
          userId: userId,
          openId: wxContext.OPENID
        })
        .limit(1)
        .get();
      
      if (userCheck.data.length === 0) {
        return {
          success: false,
          error: '无权限查看该用户的同意记录'
        };
      }
    }

    // 构建查询条件
    let whereCondition = { userId: userId };
    
    if (consentType) {
      whereCondition.consentType = consentType;
    }
    
    if (!includeHistory) {
      whereCondition.isActive = true;
    }

    // 查询同意记录
    const consentRecords = await db.collection('user_consent_records')
      .where(whereCondition)
      .orderBy('consentTime', 'desc')
      .get();

    // 处理返回数据（脱敏处理）
    const processedRecords = consentRecords.data.map(record => ({
      recordId: record._id,
      consentType: record.consentType,
      consentVersion: record.consentVersion,
      consentStatus: record.consentStatus,
      consentTime: record.consentTime,
      legalBasis: record.legalBasis,
      dataProcessingPurpose: record.dataProcessingPurpose,
      retentionPeriod: record.retentionPeriod,
      isActive: record.isActive,
      recordSource: record.recordSource,
      // 技术信息（部分脱敏）
      ipAddress: record.ipAddress ? record.ipAddress.replace(/\.\d+$/, '.***') : '',
      deviceInfo: {
        platform: record.deviceInfo?.platform || '',
        version: record.deviceInfo?.version || '',
        model: record.deviceInfo?.model || ''
      },
      createdAt: record.createdAt,
      updatedAt: record.updatedAt
    }));

    // 统计信息
    const statistics = {
      totalRecords: processedRecords.length,
      activeRecords: processedRecords.filter(r => r.isActive).length,
      consentTypes: [...new Set(processedRecords.map(r => r.consentType))],
      latestConsentTime: processedRecords.length > 0 ? processedRecords[0].consentTime : null,
      firstConsentTime: processedRecords.length > 0 ? processedRecords[processedRecords.length - 1].consentTime : null
    };

    // 记录查询日志
    await db.collection('consent_audit_logs').add({
      data: {
        userId: userId,
        action: 'consent_records_queried',
        queryParams: { consentType, includeHistory },
        timestamp: new Date(),
        ipAddress: wxContext.CLIENTIP || '',
        recordsCount: processedRecords.length,
        details: {
          source: 'user_query',
          requestor: wxContext.OPENID
        }
      }
    });

    return {
      success: true,
      message: '查询用户同意记录成功',
      data: {
        records: processedRecords,
        statistics: statistics,
        queryTime: new Date(),
        dataRetentionNotice: '根据法律要求，同意记录将保存7年用于合规举证'
      }
    };

  } catch (error) {
    console.error('[getUserConsentRecords] 查询用户同意记录失败:', error);
    
    return {
      success: false,
      error: error.message || '查询用户同意记录时发生未知错误',
      details: error
    };
  }
};
