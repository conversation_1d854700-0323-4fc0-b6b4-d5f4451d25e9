/**
 * 使用次数控制器
 * 实现AI调用次数限制，防止成本过高
 */

class UsageController {
  constructor() {
    this.MONTHLY_FREE_LIMIT = 20; // 每月免费20次
    this.STORAGE_KEY = 'ai_usage_record';
    this.init();
  }

  /**
   * 初始化使用记录
   */
  init() {
    const currentRecord = this.getCurrentMonthRecord();
    if (!currentRecord.month) {
      this.resetMonthlyRecord();
    }
  }

  /**
   * 获取当前月份记录
   */
  getCurrentMonthRecord() {
    const record = wx.getStorageSync(this.STORAGE_KEY) || {};
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM格式
    
    // 如果不是当前月份，重置记录
    if (record.month !== currentMonth) {
      return this.resetMonthlyRecord();
    }
    
    return record;
  }

  /**
   * 重置月度记录
   */
  resetMonthlyRecord() {
    const currentMonth = new Date().toISOString().slice(0, 7);
    const newRecord = {
      month: currentMonth,
      usedCount: 0,
      lastResetTime: Date.now(),
      callHistory: []
    };
    
    wx.setStorageSync(this.STORAGE_KEY, newRecord);

    return newRecord;
  }

  /**
   * 检查是否可以使用AI生成
   */
  canUseAI() {
    const record = this.getCurrentMonthRecord();
    
    // 检查次数限制
    const remaining = this.MONTHLY_FREE_LIMIT - record.usedCount;
    
    return {
      canUse: remaining > 0,
      remaining: remaining,
      usedCount: record.usedCount,
      totalLimit: this.MONTHLY_FREE_LIMIT
    };
  }

  /**
   * 消费一次AI调用
   */
  consumeUsage(commentData = {}) {
    const record = this.getCurrentMonthRecord();
    
    record.usedCount += 1;
    record.callHistory.push({
      time: Date.now(),
      studentName: commentData.studentName || '',
      commentLength: commentData.content ? commentData.content.length : 0
    });
    
    wx.setStorageSync(this.STORAGE_KEY, record);

    return record;
  }

  /**
   * 获取使用统计
   */
  getUsageStats() {
    const record = this.getCurrentMonthRecord();
    
    return {
      currentMonth: record.month,
      usedCount: record.usedCount,
      freeLimit: this.MONTHLY_FREE_LIMIT,
      remaining: Math.max(0, this.MONTHLY_FREE_LIMIT - record.usedCount),
      usageHistory: record.callHistory || [],
      resetTime: record.lastResetTime
    };
  }

  /**
   * 显示次数用完提示
   */
  showLimitReachedDialog() {
    wx.showModal({
      title: '📊 月度免费次数已用完',
      content: `━━━━━━━━━━━━━━━━━━━━━━━━
📊 本月已使用: ${this.MONTHLY_FREE_LIMIT}/${this.MONTHLY_FREE_LIMIT} 次

💡 您可以：
⏰ 等待下月自动重置免费次数
📱 继续使用其他功能
🔄 下月1号自动重置为15次

感谢您对评语灵感君的支持！
━━━━━━━━━━━━━━━━━━━━━━━━`,
      confirmText: '我知道了',
      showCancel: false
    });
  }

  /**
   * 显示剩余次数提醒
   */
  showRemainingWarning(remaining) {
    if (remaining <= 3 && remaining > 0) {
      wx.showToast({
        title: `本月还可使用${remaining}次`,
        icon: 'none',
        duration: 3000
      });
    }
  }

  /**
   * 在生成前检查并处理
   */
  async beforeGenerate() {
    const checkResult = this.canUseAI();
    
    if (!checkResult.canUse) {
      this.showLimitReachedDialog();
      return false;
    }
    
    // 剩余次数较少时提醒
    this.showRemainingWarning(checkResult.remaining);
    
    return true;
  }

  /**
   * 生成完成后记录
   */
  afterGenerate(commentData) {
    // 记录使用次数
    this.consumeUsage(commentData);
    
    return this.getUsageStats();
  }
}

// 创建全局实例
const usageController = new UsageController();

module.exports = {
  UsageController,
  usageController
};