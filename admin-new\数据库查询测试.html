<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库查询测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .result-area {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>🔍 数据库查询测试工具</h1>
    
    <div class="test-card">
        <h2>📊 AI模型数据查询</h2>
        <div style="margin-bottom: 15px;">
            <button class="test-button" onclick="queryAiConfigs()">查询ai_configs集合</button>
            <button class="test-button" onclick="querySystemConfig()">查询system_config集合</button>
            <button class="test-button" onclick="testGetModelsAPI()">测试getModels API</button>
            <button class="test-button" onclick="compareData()">对比数据差异</button>
        </div>
        <div id="queryResults" class="result-area"></div>
    </div>

    <div class="test-card">
        <h2>📋 数据详情</h2>
        <div id="dataDetails"></div>
    </div>

    <script src="https://web.sdk.qcloud.com/tcb-js-sdk/1.19.2/tcb.js"></script>
    <script>
        let cloudbaseApp = null;
        let queryResults = '';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            queryResults += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            document.getElementById('queryResults').innerHTML = queryResults;
            document.getElementById('queryResults').scrollTop = document.getElementById('queryResults').scrollHeight;
        }

        async function initCloudbase() {
            try {
                if (cloudbaseApp) return cloudbaseApp;

                cloudbaseApp = tcb.init({
                    env: 'cloud1-4g85f8xlb8166ff1',
                    region: 'ap-shanghai'
                });

                const auth = cloudbaseApp.auth();
                await auth.signInAnonymously();
                
                log('✅ 云开发SDK初始化成功', 'success');
                return cloudbaseApp;
            } catch (error) {
                log('❌ 云开发SDK初始化失败: ' + error.message, 'error');
                throw error;
            }
        }

        async function queryAiConfigs() {
            log('🔄 开始查询ai_configs集合...', 'info');
            
            try {
                const app = await initCloudbase();
                const db = app.database();
                
                // 直接查询数据库
                const result = await db.collection('ai_configs').limit(100).get();
                
                log('📊 ai_configs查询结果:', 'success');
                log(`总数量: ${result.data.length}`, 'info');
                
                if (result.data.length > 0) {
                    log('详细数据:', 'info');
                    result.data.forEach((item, index) => {
                        log(`${index + 1}. ID: ${item._id}`, 'info');
                        log(`   名称: ${item.name || '未设置'}`, 'info');
                        log(`   提供商: ${item.provider || '未设置'}`, 'info');
                        log(`   模型: ${item.model || '未设置'}`, 'info');
                        log(`   状态: ${item.status || '未设置'}`, 'info');
                        log(`   创建时间: ${item.createTime || item.createdAt || '未设置'}`, 'info');
                        log('   ---', 'info');
                    });
                    
                    // 显示表格
                    displayDataTable(result.data, 'ai_configs');
                } else {
                    log('❌ 没有找到任何ai_configs数据', 'warning');
                }
                
            } catch (error) {
                log('❌ 查询ai_configs失败: ' + error.message, 'error');
            }
        }

        async function querySystemConfig() {
            log('🔄 开始查询system_config集合...', 'info');
            
            try {
                const app = await initCloudbase();
                const db = app.database();
                
                const result = await db.collection('system_config').where({
                    type: 'ai_config'
                }).get();
                
                log('📊 system_config查询结果:', 'success');
                log(`AI配置数量: ${result.data.length}`, 'info');
                
                if (result.data.length > 0) {
                    result.data.forEach((item, index) => {
                        log(`${index + 1}. ID: ${item._id}`, 'info');
                        log(`   模型: ${item.model || '未设置'}`, 'info');
                        log(`   提供商: ${item.provider || '未设置'}`, 'info');
                        log(`   状态: ${item.status || '未设置'}`, 'info');
                        log(`   API密钥: ${item.apiKey ? '已设置' : '未设置'}`, 'info');
                        log('   ---', 'info');
                    });
                }
                
            } catch (error) {
                log('❌ 查询system_config失败: ' + error.message, 'error');
            }
        }

        async function testGetModelsAPI() {
            log('🔄 开始测试getModels API...', 'info');
            
            try {
                const app = await initCloudbase();
                const result = await app.callFunction({
                    name: 'adminAPI',
                    data: { action: 'ai.getModels' }
                });

                log('📊 getModels API结果:', 'success');
                
                if (result.result && result.result.code === 200) {
                    const models = result.result.data;
                    log(`API返回模型数量: ${models.length}`, 'info');
                    
                    models.forEach((model, index) => {
                        log(`${index + 1}. ID: ${model.id}`, 'info');
                        log(`   名称: ${model.name}`, 'info');
                        log(`   提供商: ${model.provider}`, 'info');
                        log(`   模型: ${model.model}`, 'info');
                        log(`   状态: ${model.status}`, 'info');
                        log('   ---', 'info');
                    });
                    
                    // 显示API返回的数据表格
                    displayDataTable(models, 'api_result');
                } else {
                    log('❌ API调用失败: ' + JSON.stringify(result.result), 'error');
                }
                
            } catch (error) {
                log('❌ 测试getModels API失败: ' + error.message, 'error');
            }
        }

        async function compareData() {
            log('🔄 开始对比数据差异...', 'info');
            
            try {
                const app = await initCloudbase();
                const db = app.database();
                
                // 获取数据库原始数据
                const dbResult = await db.collection('ai_configs').limit(100).get();
                
                // 获取API返回数据
                const apiResult = await app.callFunction({
                    name: 'adminAPI',
                    data: { action: 'ai.getModels' }
                });
                
                log('📊 数据对比结果:', 'success');
                log(`数据库原始数据: ${dbResult.data.length} 条`, 'info');
                log(`API返回数据: ${apiResult.result?.data?.length || 0} 条`, 'info');
                
                if (dbResult.data.length !== (apiResult.result?.data?.length || 0)) {
                    log('⚠️ 数据数量不一致！', 'warning');
                    
                    // 分析差异
                    const dbIds = dbResult.data.map(item => item._id);
                    const apiIds = apiResult.result?.data?.map(item => item.id) || [];
                    
                    const missingInApi = dbIds.filter(id => !apiIds.includes(id));
                    const extraInApi = apiIds.filter(id => !dbIds.includes(id));
                    
                    if (missingInApi.length > 0) {
                        log(`❌ 数据库中存在但API中缺失的ID: ${missingInApi.join(', ')}`, 'error');
                    }
                    
                    if (extraInApi.length > 0) {
                        log(`➕ API中存在但数据库中没有的ID: ${extraInApi.join(', ')}`, 'warning');
                    }
                } else {
                    log('✅ 数据数量一致', 'success');
                }
                
            } catch (error) {
                log('❌ 对比数据失败: ' + error.message, 'error');
            }
        }

        function displayDataTable(data, type) {
            const detailsDiv = document.getElementById('dataDetails');
            
            if (!data || data.length === 0) {
                detailsDiv.innerHTML = '<p>没有数据可显示</p>';
                return;
            }
            
            let html = `<h3>${type === 'ai_configs' ? '数据库原始数据' : 'API返回数据'}</h3>`;
            html += '<table>';
            html += '<tr><th>ID</th><th>名称</th><th>提供商</th><th>模型</th><th>状态</th><th>创建时间</th></tr>';
            
            data.forEach(item => {
                html += '<tr>';
                html += `<td>${item._id || item.id || '无'}</td>`;
                html += `<td>${item.name || '无'}</td>`;
                html += `<td>${item.provider || '无'}</td>`;
                html += `<td>${item.model || '无'}</td>`;
                html += `<td>${item.status || '无'}</td>`;
                html += `<td>${item.createTime || item.createdAt || '无'}</td>`;
                html += '</tr>';
            });
            
            html += '</table>';
            detailsDiv.innerHTML = html;
        }

        // 页面加载时自动初始化
        window.onload = function() {
            log('🚀 数据库查询测试工具已启动', 'info');
            log('💡 点击按钮开始查询数据库中的AI模型配置', 'info');
        };
    </script>
</body>
</html>
